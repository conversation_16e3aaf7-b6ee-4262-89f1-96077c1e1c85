# 返利系统监控体系配置
# Prometheus + Grafana + AlertManager

---
# Prometheus配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'rebate-system'
        environment: 'production'
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      # Kubernetes API Server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
          - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
            action: keep
            regex: default;kubernetes;https
      
      # Kubernetes Nodes
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - target_label: __address__
            replacement: kubernetes.default.svc:443
          - source_labels: [__meta_kubernetes_node_name]
            regex: (.+)
            target_label: __metrics_path__
            replacement: /api/v1/nodes/${1}/proxy/metrics
      
      # 返利系统微服务
      - job_name: 'rebate-microservices'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - rebate-system
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_service_name]
            action: replace
            target_label: kubernetes_name
      
      # MySQL数据库监控
      - job_name: 'mysql-exporter'
        static_configs:
          - targets: ['mysql-exporter:9104']
        scrape_interval: 30s
      
      # Redis监控
      - job_name: 'redis-exporter'
        static_configs:
          - targets: ['redis-exporter:9121']
        scrape_interval: 30s
      
      # RabbitMQ监控
      - job_name: 'rabbitmq-exporter'
        static_configs:
          - targets: ['rabbitmq-exporter:9419']
        scrape_interval: 30s

---
# 告警规则配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: monitoring
data:
  rebate-system-rules.yml: |
    groups:
      - name: rebate-system-alerts
        rules:
          # 服务可用性告警
          - alert: ServiceDown
            expr: up{job="rebate-microservices"} == 0
            for: 1m
            labels:
              severity: critical
              team: backend
            annotations:
              summary: "服务 {{ $labels.kubernetes_name }} 不可用"
              description: "服务 {{ $labels.kubernetes_name }} 在命名空间 {{ $labels.kubernetes_namespace }} 中已经下线超过1分钟"
          
          # 高错误率告警
          - alert: HighErrorRate
            expr: |
              (
                rate(http_requests_total{status=~"5.."}[5m]) /
                rate(http_requests_total[5m])
              ) * 100 > 5
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "服务 {{ $labels.service }} 错误率过高"
              description: "服务 {{ $labels.service }} 的错误率在过去5分钟内超过5%，当前值: {{ $value }}%"
          
          # 响应时间告警
          - alert: HighResponseTime
            expr: |
              histogram_quantile(0.95, 
                rate(http_request_duration_seconds_bucket[5m])
              ) > 2
            for: 5m
            labels:
              severity: warning
              team: backend
            annotations:
              summary: "服务 {{ $labels.service }} 响应时间过长"
              description: "服务 {{ $labels.service }} 的95%响应时间超过2秒，当前值: {{ $value }}秒"
          
          # CPU使用率告警
          - alert: HighCPUUsage
            expr: |
              (
                rate(container_cpu_usage_seconds_total{pod=~".*rebate.*"}[5m]) * 100
              ) > 80
            for: 10m
            labels:
              severity: warning
              team: ops
            annotations:
              summary: "Pod {{ $labels.pod }} CPU使用率过高"
              description: "Pod {{ $labels.pod }} 的CPU使用率超过80%，当前值: {{ $value }}%"
          
          # 内存使用率告警
          - alert: HighMemoryUsage
            expr: |
              (
                container_memory_working_set_bytes{pod=~".*rebate.*"} /
                container_spec_memory_limit_bytes{pod=~".*rebate.*"}
              ) * 100 > 85
            for: 10m
            labels:
              severity: warning
              team: ops
            annotations:
              summary: "Pod {{ $labels.pod }} 内存使用率过高"
              description: "Pod {{ $labels.pod }} 的内存使用率超过85%，当前值: {{ $value }}%"
          
          # 数据库连接数告警
          - alert: HighDatabaseConnections
            expr: mysql_global_status_threads_connected > 80
            for: 5m
            labels:
              severity: warning
              team: dba
            annotations:
              summary: "MySQL连接数过高"
              description: "MySQL数据库连接数超过80，当前值: {{ $value }}"
          
          # 数据同步延迟告警
          - alert: DataSyncDelay
            expr: |
              (time() - data_sync_last_success_timestamp) > 300
            for: 1m
            labels:
              severity: critical
              team: backend
            annotations:
              summary: "数据同步延迟"
              description: "SAP HANA到MySQL的数据同步延迟超过5分钟"
          
          # 返利计算失败告警
          - alert: RebateCalculationFailed
            expr: increase(rebate_calculation_failed_total[1h]) > 0
            for: 1m
            labels:
              severity: critical
              team: business
            annotations:
              summary: "返利计算失败"
              description: "过去1小时内有 {{ $value }} 次返利计算失败"
          
          # 磁盘空间告警
          - alert: DiskSpaceUsage
            expr: |
              (
                node_filesystem_size_bytes{fstype!="tmpfs"} -
                node_filesystem_free_bytes{fstype!="tmpfs"}
              ) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 85
            for: 10m
            labels:
              severity: warning
              team: ops
            annotations:
              summary: "磁盘空间使用率过高"
              description: "节点 {{ $labels.instance }} 的磁盘 {{ $labels.device }} 使用率超过85%，当前值: {{ $value }}%"

---
# AlertManager配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.company.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'password'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default-receiver'
      routes:
        - match:
            severity: critical
          receiver: 'critical-alerts'
          group_wait: 5s
          repeat_interval: 30m
        
        - match:
            team: backend
          receiver: 'backend-team'
        
        - match:
            team: ops
          receiver: 'ops-team'
        
        - match:
            team: dba
          receiver: 'dba-team'
    
    receivers:
      - name: 'default-receiver'
        email_configs:
          - to: '<EMAIL>'
            subject: '[返利系统] {{ .GroupLabels.alertname }}'
            body: |
              {{ range .Alerts }}
              告警: {{ .Annotations.summary }}
              描述: {{ .Annotations.description }}
              时间: {{ .StartsAt }}
              标签: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
              {{ end }}
      
      - name: 'critical-alerts'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: '[紧急] 返利系统严重告警'
            body: |
              ⚠️ 严重告警 ⚠️
              {{ range .Alerts }}
              告警: {{ .Annotations.summary }}
              描述: {{ .Annotations.description }}
              时间: {{ .StartsAt }}
              {{ end }}
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
            channel: '#critical-alerts'
            title: '返利系统严重告警'
            text: |
              {{ range .Alerts }}
              🚨 {{ .Annotations.summary }}
              {{ .Annotations.description }}
              {{ end }}
      
      - name: 'backend-team'
        email_configs:
          - to: '<EMAIL>'
            subject: '[返利系统] 后端服务告警'
      
      - name: 'ops-team'
        email_configs:
          - to: '<EMAIL>'
            subject: '[返利系统] 运维告警'
      
      - name: 'dba-team'
        email_configs:
          - to: '<EMAIL>'
            subject: '[返利系统] 数据库告警'

---
# Grafana Dashboard配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: monitoring
data:
  rebate-system-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "返利系统总览",
        "tags": ["rebate-system"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "服务状态",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=\"rebate-microservices\"}",
                "legendFormat": "{{ kubernetes_name }}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                }
              }
            }
          },
          {
            "id": 2,
            "title": "请求QPS",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m])) by (service)",
                "legendFormat": "{{ service }}"
              }
            ]
          },
          {
            "id": 3,
            "title": "响应时间",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                "legendFormat": "95th percentile"
              },
              {
                "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
                "legendFormat": "50th percentile"
              }
            ]
          },
          {
            "id": 4,
            "title": "错误率",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
                "legendFormat": "{{ service }}"
              }
            ]
          },
          {
            "id": 5,
            "title": "数据同步状态",
            "type": "stat",
            "targets": [
              {
                "expr": "data_sync_success_total",
                "legendFormat": "成功次数"
              },
              {
                "expr": "data_sync_failed_total",
                "legendFormat": "失败次数"
              }
            ]
          },
          {
            "id": 6,
            "title": "返利计算统计",
            "type": "graph",
            "targets": [
              {
                "expr": "increase(rebate_calculation_total[1h])",
                "legendFormat": "计算次数"
              },
              {
                "expr": "increase(rebate_calculation_failed_total[1h])",
                "legendFormat": "失败次数"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
