/**
 * 合同管理服务 - 核心业务逻辑实现
 * 对应SAP ZRED0040功能模块
 */
@RestController
@RequestMapping("/api/v1/contracts")
@Slf4j
@Validated
public class ContractController {

    @Autowired
    private ContractService contractService;
    
    @Autowired
    private DataSyncService dataSyncService;

    /**
     * 创建合同
     */
    @PostMapping
    @Operation(summary = "创建返利合同", description = "创建新的返利合同，支持双写SAP HANA和MySQL")
    public ResponseEntity<ApiResponse<ContractDTO>> createContract(
            @Valid @RequestBody ContractCreateRequest request) {
        
        try {
            ContractDTO contract = contractService.createContract(request);
            return ResponseEntity.ok(ApiResponse.success(contract));
        } catch (BusinessException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新合同
     */
    @PutMapping("/{contractId}")
    @Operation(summary = "更新返利合同")
    public ResponseEntity<ApiResponse<ContractDTO>> updateContract(
            @PathVariable String contractId,
            @Valid @RequestBody ContractUpdateRequest request) {
        
        try {
            ContractDTO contract = contractService.updateContract(contractId, request);
            return ResponseEntity.ok(ApiResponse.success(contract));
        } catch (BusinessException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 查询合同详情
     */
    @GetMapping("/{contractId}")
    @Operation(summary = "查询合同详情")
    public ResponseEntity<ApiResponse<ContractDTO>> getContract(
            @PathVariable String contractId) {
        
        try {
            ContractDTO contract = contractService.getContractById(contractId);
            return ResponseEntity.ok(ApiResponse.success(contract));
        } catch (BusinessException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 分页查询合同列表
     */
    @GetMapping
    @Operation(summary = "分页查询合同列表")
    public ResponseEntity<ApiResponse<PageResult<ContractDTO>>> getContracts(
            @Valid ContractQueryRequest request) {
        
        try {
            PageResult<ContractDTO> result = contractService.getContracts(request);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 提交审批
     */
    @PostMapping("/{contractId}/submit")
    @Operation(summary = "提交合同审批")
    public ResponseEntity<ApiResponse<Void>> submitForApproval(
            @PathVariable String contractId) {
        
        try {
            contractService.submitForApproval(contractId);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (BusinessException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        }
    }
}

/**
 * 合同服务实现类
 */
@Service
@Transactional
@Slf4j
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractRepository contractRepository;
    
    @Autowired
    private DataSyncService dataSyncService;
    
    @Autowired
    private ApprovalService approvalService;
    
    @Autowired
    private ProductGroupService productGroupService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public ContractDTO createContract(ContractCreateRequest request) {
        // 1. 数据验证
        validateContractData(request);
        
        // 2. 生成合同编号
        String contractId = generateContractId();
        
        // 3. 构建合同实体
        ContractEntity contract = buildContractEntity(contractId, request);
        
        // 4. 双写数据库
        try {
            ContractEntity savedContract = dataSyncService.createWithDualWrite(
                contract, "CONTRACT");
            
            // 5. 处理商品组
            if (request.getProductGroups() != null && !request.getProductGroups().isEmpty()) {
                productGroupService.createProductGroups(contractId, request.getProductGroups());
            }
            
            // 6. 缓存合同信息
            cacheContract(savedContract);
            
            return convertToDTO(savedContract);
            
        } catch (Exception e) {
            log.error("合同创建失败: contractId={}", contractId, e);
            throw new BusinessException("合同创建失败: " + e.getMessage());
        }
    }

    @Override
    public ContractDTO updateContract(String contractId, ContractUpdateRequest request) {
        // 1. 检查合同存在性和状态
        ContractEntity existingContract = contractRepository.findById(contractId)
            .orElseThrow(() -> new BusinessException("合同不存在: " + contractId));
        
        if (!canModifyContract(existingContract)) {
            throw new BusinessException("合同当前状态不允许修改");
        }
        
        // 2. 数据验证
        validateUpdateData(request, existingContract);
        
        // 3. 更新合同数据
        updateContractFields(existingContract, request);
        
        // 4. 双写更新
        try {
            ContractEntity updatedContract = dataSyncService.updateWithDualWrite(
                existingContract, "CONTRACT");
            
            // 5. 更新缓存
            cacheContract(updatedContract);
            
            return convertToDTO(updatedContract);
            
        } catch (Exception e) {
            log.error("合同更新失败: contractId={}", contractId, e);
            throw new BusinessException("合同更新失败: " + e.getMessage());
        }
    }

    @Override
    public ContractDTO getContractById(String contractId) {
        // 1. 先从缓存获取
        ContractDTO cachedContract = getCachedContract(contractId);
        if (cachedContract != null) {
            return cachedContract;
        }
        
        // 2. 从数据库查询
        ContractEntity contract = contractRepository.findById(contractId)
            .orElseThrow(() -> new BusinessException("合同不存在: " + contractId));
        
        ContractDTO contractDTO = convertToDTO(contract);
        
        // 3. 缓存结果
        cacheContract(contract);
        
        return contractDTO;
    }

    @Override
    public PageResult<ContractDTO> getContracts(ContractQueryRequest request) {
        // 构建查询条件
        Specification<ContractEntity> spec = buildQuerySpecification(request);
        
        // 分页查询
        Pageable pageable = PageRequest.of(
            request.getPage() - 1, 
            request.getSize(),
            Sort.by(Sort.Direction.DESC, "createdDate")
        );
        
        Page<ContractEntity> page = contractRepository.findAll(spec, pageable);
        
        // 转换结果
        List<ContractDTO> contracts = page.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return PageResult.<ContractDTO>builder()
            .records(contracts)
            .total(page.getTotalElements())
            .current(request.getPage())
            .size(request.getSize())
            .build();
    }

    @Override
    public void submitForApproval(String contractId) {
        ContractEntity contract = contractRepository.findById(contractId)
            .orElseThrow(() -> new BusinessException("合同不存在: " + contractId));
        
        // 检查是否可以提交审批
        if (!canSubmitForApproval(contract)) {
            throw new BusinessException("合同当前状态不允许提交审批");
        }
        
        // 提交审批流程
        approvalService.submitContractApproval(contract);
        
        // 更新合同状态
        contract.setStatus("02"); // 待审批
        contract.setUpdatedBy(getCurrentUser());
        contract.setUpdatedDate(LocalDate.now());
        contract.setUpdatedTime(LocalTime.now());
        
        // 双写更新
        dataSyncService.updateWithDualWrite(contract, "CONTRACT");
        
        // 更新缓存
        cacheContract(contract);
    }

    /**
     * 数据验证
     */
    private void validateContractData(ContractCreateRequest request) {
        // 1. 必填字段验证
        if (StringUtils.isEmpty(request.getPartnerCode())) {
            throw new ValidationException("伙伴编码不能为空");
        }
        
        // 2. 伙伴代码验证 (对应SAP中的frm_check_partner)
        if (!isValidPartner(request.getPartnerType(), request.getPartnerCode())) {
            throw new ValidationException("伙伴不存在或无效");
        }
        
        // 3. 日期逻辑验证
        if (request.getStartDate().after(request.getEndDate())) {
            throw new ValidationException("开始日期不能大于结束日期");
        }
        
        // 4. 合同重复性检查
        if (isDuplicateContract(request)) {
            throw new ValidationException("存在重复的合同");
        }
        
        // 5. 公司代码验证
        if (!isValidCompanyCode(request.getCompanyCode())) {
            throw new ValidationException("公司代码无效");
        }
    }

    /**
     * 生成合同编号 (对应SAP的编号规则)
     */
    private String generateContractId() {
        // 格式: ZRE + 年份 + 序号 (如: ZRE202400001)
        String year = String.valueOf(LocalDate.now().getYear());
        String prefix = "ZRE" + year;
        
        // 使用Redis实现分布式序号生成
        String key = "contract:sequence:" + year;
        Long sequence = redisTemplate.opsForValue().increment(key);
        
        if (sequence == 1) {
            // 设置过期时间为2年
            redisTemplate.expire(key, Duration.ofDays(730));
        }
        
        return prefix + String.format("%05d", sequence);
    }

    /**
     * 缓存管理
     */
    private void cacheContract(ContractEntity contract) {
        try {
            String cacheKey = "contract:" + contract.getContractId();
            ContractDTO contractDTO = convertToDTO(contract);
            redisTemplate.opsForValue().set(cacheKey, contractDTO, Duration.ofHours(24));
        } catch (Exception e) {
            log.warn("合同缓存失败: {}", contract.getContractId(), e);
        }
    }

    private ContractDTO getCachedContract(String contractId) {
        try {
            String cacheKey = "contract:" + contractId;
            return (ContractDTO) redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("获取合同缓存失败: {}", contractId, e);
            return null;
        }
    }

    /**
     * 实体转换
     */
    private ContractDTO convertToDTO(ContractEntity entity) {
        return ContractDTO.builder()
            .contractId(entity.getContractId())
            .contractType(entity.getContractType())
            .companyCode(entity.getCompanyCode())
            .contractDesc(entity.getContractDesc())
            .partnerType(entity.getPartnerType())
            .partnerCode(entity.getPartnerCode())
            .startDate(entity.getStartDate())
            .endDate(entity.getEndDate())
            .status(entity.getStatus())
            .approvalStatus(entity.getApprovalStatus())
            .createdBy(entity.getCreatedBy())
            .createdDate(entity.getCreatedDate())
            .build();
    }
}
