'use client'

import React from 'react'

export default function Diagram8() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-4">
      <div className="max-w-[1800px] mx-auto h-screen flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-white">"开着飞机换引擎" - 一期技术架构演进图</h1>
            <div className="px-3 py-1 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full text-white text-xs font-semibold">
              Zero-Downtime Migration Strategy
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                <div className="w-3 h-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold text-sm">商济健康</div>
              <div className="text-red-300 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Architecture Diagram */}
        <div className="flex-1 bg-gradient-to-br from-slate-800/50 to-gray-800/50 rounded-2xl p-6 border border-slate-600/50 relative overflow-hidden">
          {/* Architecture SVG */}
          <svg viewBox="0 0 1600 1000" className="w-full h-full">
            <defs>
              {/* Gradients */}
              <linearGradient id="sapGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#991b1b" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="syncGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#0891b2" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#0e7490" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="newGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#059669" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#047857" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="evolutionGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#7c3aed" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#5b21b6" stopOpacity="0.9"/>
              </linearGradient>

              {/* Arrow markers */}
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#0891b2"/>
              </marker>
              <marker id="evolutionArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                <polygon points="0 0, 12 4, 0 8" fill="#7c3aed"/>
              </marker>
            </defs>

            {/* Background grid */}
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#374151" strokeWidth="1" opacity="0.3"/>
            </pattern>
            <rect width="1600" height="1000" fill="url(#grid)"/>

            {/* Title */}
            <text x="800" y="35" textAnchor="middle" fontSize="26" fontWeight="bold" fill="#f8fafc">
              "开着飞机换引擎" - 一期技术架构演进策略
            </text>
            <text x="800" y="55" textAnchor="middle" fontSize="14" fill="#94a3b8">
              SAP+HANA → 微服务+MySQL 渐进式改造 | 双写并行 | 零业务中断
            </text>

            {/* Evolution Timeline */}
            <g id="evolutionTimeline">
              <rect x="50" y="80" width="1500" height="60" rx="8" fill="url(#evolutionGrad)" stroke="#7c3aed" strokeWidth="2"/>
              <text x="800" y="105" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">技术架构演进时间线</text>
              <text x="800" y="125" textAnchor="middle" fontSize="12" fill="white">现有SAP架构 → 一期双写并行 → 二期计算引擎迁移 → 三期完全微服务化</text>
            </g>

            {/* Current SAP System */}
            <g id="sapSystem">
              <rect x="50" y="160" width="350" height="650" rx="15" fill="url(#sapGrad)" stroke="#dc2626" strokeWidth="3"/>
              <text x="225" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">现有SAP架构</text>
              <text x="225" y="205" textAnchor="middle" fontSize="12" fill="#fbbf24">需要逐步改造的传统系统</text>

              {/* SAP GUI */}
              <rect x="70" y="220" width="310" height="70" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="225" y="240" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">SAP GUI 界面层</text>
              <text x="80" y="260" fontSize="11" fill="white">• 传统桌面客户端 • 用户体验陈旧 • 移动端不支持</text>
              <text x="80" y="275" fontSize="11" fill="white">• 响应时间: 3-5秒 • 并发限制: 50用户</text>

              {/* ABAP Programs */}
              <rect x="70" y="310" width="100" height="110" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="120" y="330" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZRED0040</text>
              <text x="120" y="345" textAnchor="middle" fontSize="10" fill="white">合同管理</text>
              <text x="80" y="360" fontSize="9" fill="white">• 创建合同</text>
              <text x="80" y="375" fontSize="9" fill="white">• 修改合同</text>
              <text x="80" y="390" fontSize="9" fill="white">• 审批流程</text>
              <text x="80" y="405" fontSize="9" fill="white">• 状态管理</text>

              <rect x="180" y="310" width="100" height="110" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="230" y="330" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZRED0041</text>
              <text x="230" y="345" textAnchor="middle" fontSize="10" fill="white">条款管理</text>
              <text x="190" y="360" fontSize="9" fill="white">• 条款配置</text>
              <text x="190" y="375" fontSize="9" fill="white">• 算法设置</text>
              <text x="190" y="390" fontSize="9" fill="white">• 参数验证</text>
              <text x="190" y="405" fontSize="9" fill="white">• 效果分析</text>

              <rect x="290" y="310" width="90" height="110" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="335" y="330" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZRED0056</text>
              <text x="335" y="345" textAnchor="middle" fontSize="10" fill="white">协议管理</text>
              <text x="300" y="360" fontSize="9" fill="white">• 协议生成</text>
              <text x="300" y="375" fontSize="9" fill="white">• 批量处理</text>
              <text x="300" y="390" fontSize="9" fill="white">• 组织管理</text>
              <text x="300" y="405" fontSize="9" fill="white">• 付款验证</text>

              {/* SAP HANA Database */}
              <rect x="70" y="440" width="310" height="100" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="225" y="460" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">SAP HANA 数据库</text>
              <text x="80" y="480" fontSize="10" fill="white">• ZRETA001(合同) • ZRETA002(条款) • ZRET0006(协议)</text>
              <text x="80" y="495" fontSize="10" fill="white">• 触发器机制 • 实时数据捕获 • 变更日志记录</text>
              <text x="80" y="510" fontSize="10" fill="white">• CREATE TRIGGER sync_contract_changes</text>
              <text x="80" y="525" fontSize="10" fill="white">• 数据完整性约束 • 零延迟触发</text>

              {/* Current Issues */}
              <rect x="70" y="560" width="310" height="120" rx="8" fill="rgba(239, 68, 68, 0.2)" stroke="#ef4444" strokeWidth="2"/>
              <text x="225" y="580" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#ef4444">现有系统痛点</text>
              <text x="80" y="600" fontSize="11" fill="white">🔴 技术栈老化：ABAP开发效率低，人才稀缺</text>
              <text x="80" y="615" fontSize="11" fill="white">🔴 用户体验差：GUI界面陈旧，移动端不支持</text>
              <text x="80" y="630" fontSize="11" fill="white">🔴 性能瓶颈：响应慢，并发能力有限</text>
              <text x="80" y="645" fontSize="11" fill="white">🔴 扩展困难：集中式架构，难以快速迭代</text>
              <text x="80" y="660" fontSize="11" fill="white">🔴 运维复杂：依赖SAP专业运维团队</text>

              {/* Migration Strategy */}
              <rect x="70" y="700" width="310" height="100" rx="8" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="2"/>
              <text x="225" y="720" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#22c55e">一期改造策略</text>
              <text x="80" y="740" fontSize="11" fill="white">✅ 保持SAP核心业务逻辑不变</text>
              <text x="80" y="755" fontSize="11" fill="white">✅ 构建现代化管理界面</text>
              <text x="80" y="770" fontSize="11" fill="white">✅ 建立双写数据同步机制</text>
              <text x="80" y="785" fontSize="11" fill="white">✅ 为后续微服务化奠定基础</text>
            </g>

            {/* Data Sync Bridge Layer */}
            <g id="syncLayer">
              <rect x="420" y="160" width="280" height="650" rx="15" fill="url(#syncGrad)" stroke="#0891b2" strokeWidth="3"/>
              <text x="560" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">双写同步桥接层</text>
              <text x="560" y="205" textAnchor="middle" fontSize="12" fill="#fbbf24">"开着飞机换引擎"核心机制</text>

              {/* Trigger Mechanism */}
              <rect x="440" y="220" width="240" height="90" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="560" y="240" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">SAP HANA 触发器</text>
              <text x="450" y="260" fontSize="10" fill="white">• INSERT/UPDATE/DELETE 事件实时捕获</text>
              <text x="450" y="275" fontSize="10" fill="white">• 零延迟数据变更检测机制</text>
              <text x="450" y="290" fontSize="10" fill="white">• 自动写入ZSYNC_QUEUE同步队列</text>
              <text x="450" y="305" fontSize="10" fill="white">• 支持批量操作和事务一致性</text>

              {/* Sync Queue Processing */}
              <rect x="440" y="330" width="240" height="90" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="560" y="350" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">同步队列处理</text>
              <text x="450" y="370" fontSize="10" fill="white">• Java微服务每5秒轮询队列</text>
              <text x="450" y="385" fontSize="10" fill="white">• 异步消息处理机制</text>
              <text x="450" y="400" fontSize="10" fill="white">• 失败重试 + 死信队列处理</text>
              <text x="450" y="415" fontSize="10" fill="white">• 消息持久化存储保障</text>

              {/* Data Transformation */}
              <rect x="440" y="440" width="240" height="90" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="560" y="460" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">数据转换引擎</text>
              <text x="450" y="480" fontSize="10" fill="white">• SAP表结构 → MySQL表结构映射</text>
              <text x="450" y="495" fontSize="10" fill="white">• 字段类型转换和数据清洗</text>
              <text x="450" y="510" fontSize="10" fill="white">• 业务规则验证和数据校验</text>
              <text x="450" y="525" fontSize="10" fill="white">• 异构数据库兼容性处理</text>

              {/* Data Validation */}
              <rect x="440" y="550" width="240" height="90" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="560" y="570" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">一致性校验机制</text>
              <text x="450" y="590" fontSize="10" fill="white">• 定时数据一致性检查(每10分钟)</text>
              <text x="450" y="605" fontSize="10" fill="white">• MD5哈希值比对验证</text>
              <text x="450" y="620" fontSize="10" fill="white">• 自动差异检测和修复</text>
              <text x="450" y="635" fontSize="10" fill="white">• 数据漂移告警和人工介入</text>

              {/* Sync Monitor */}
              <rect x="440" y="660" width="240" height="90" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="560" y="680" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">同步监控中心</text>
              <text x="450" y="700" fontSize="10" fill="white">• 实时同步状态监控面板</text>
              <text x="450" y="715" fontSize="10" fill="white">• 同步延迟和成功率统计</text>
              <text x="450" y="730" fontSize="10" fill="white">• 异常告警和通知机制</text>
              <text x="450" y="745" fontSize="10" fill="white">• 性能指标和趋势分析</text>

              {/* Performance Metrics */}
              <rect x="440" y="770" width="240" height="30" rx="8" fill="rgba(34, 197, 94, 0.3)" stroke="#22c55e" strokeWidth="2"/>
              <text x="560" y="790" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">同步性能: &lt;10秒延迟 | 99.99%一致性 | 24/7运行</text>
            </g>

            {/* New Rebate Engine System */}
            <g id="rebateEngine">
              <rect x="720" y="160" width="830" height="650" rx="15" fill="url(#newGrad)" stroke="#059669" strokeWidth="3"/>
              <text x="1135" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">返利引擎 - 一期能力构建</text>
              <text x="1135" y="205" textAnchor="middle" fontSize="12" fill="#fbbf24">现代化微服务架构 + 双写并行运行</text>

              {/* Amis Low-code Frontend */}
              <rect x="740" y="220" width="790" height="70" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1135" y="240" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">Amis 低代码前端层</text>
              <text x="750" y="260" fontSize="10" fill="white">• 合同管理页面 • 条款配置页面 • 协议管理页面 • 数据同步监控页面 • 审批流程页面</text>
              <text x="750" y="275" fontSize="10" fill="white">• 替代SAP GUI • 现代化用户体验 • 移动端支持 • 响应时间&lt;500ms • 支持个性化定制</text>

              {/* Microservices Architecture */}
              <rect x="740" y="310" width="790" height="160" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1135" y="330" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">微服务架构 - 核心业务引擎</text>

              <rect x="760" y="350" width="180" height="110" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="850" y="370" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">合同管理引擎</text>
              <text x="770" y="385" fontSize="9" fill="white">• ContractController.create()</text>
              <text x="770" y="400" fontSize="9" fill="white">• WorkflowService.approve()</text>
              <text x="770" y="415" fontSize="9" fill="white">• ContractStateService.update()</text>
              <text x="770" y="430" fontSize="9" fill="white">• 合同生命周期管理</text>
              <text x="770" y="445" fontSize="9" fill="white">• 审批流程引擎</text>

              <rect x="960" y="350" width="180" height="110" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1050" y="370" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">条款配置引擎</text>
              <text x="970" y="385" fontSize="9" fill="white">• ClauseConfigService.config()</text>
              <text x="970" y="400" fontSize="9" fill="white">• AlgorithmService.setup()</text>
              <text x="970" y="415" fontSize="9" fill="white">• ValidationEngine.validate()</text>
              <text x="970" y="430" fontSize="9" fill="white">• 返利算法配置</text>
              <text x="970" y="445" fontSize="9" fill="white">• 参数验证引擎</text>

              <rect x="1160" y="350" width="180" height="110" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1250" y="370" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">协议生成引擎</text>
              <text x="1170" y="385" fontSize="9" fill="white">• AgreementGenerator</text>
              <text x="1170" y="400" fontSize="9" fill="white">• BatchProcessService</text>
              <text x="1170" y="415" fontSize="9" fill="white">• OrganizationService</text>
              <text x="1170" y="430" fontSize="9" fill="white">• 协议模板引擎</text>
              <text x="1170" y="445" fontSize="9" fill="white">• 批量处理能力</text>

              <rect x="1360" y="350" width="160" height="110" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1440" y="370" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">数据同步服务</text>
              <text x="1370" y="385" fontSize="9" fill="white">• DataSyncService</text>
              <text x="1370" y="400" fontSize="9" fill="white">• ConsistencyChecker</text>
              <text x="1370" y="415" fontSize="9" fill="white">• CompensationHandler</text>
              <text x="1370" y="430" fontSize="9" fill="white">• 实时同步监控</text>
              <text x="1370" y="445" fontSize="9" fill="white">• 异常恢复机制</text>

              {/* Dual Database Architecture */}
              <rect x="740" y="490" width="790" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1135" y="510" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">双数据库架构 - 异构表设计</text>

              <rect x="760" y="530" width="360" height="70" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="940" y="550" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">MySQL 异构表结构映射</text>
              <text x="770" y="570" fontSize="9" fill="white">• rebate_contract (合同表) ← ZRETA001 • rebate_clause (条款表) ← ZRETA002</text>
              <text x="770" y="585" fontSize="9" fill="white">• rebate_agreement (协议表) ← ZRET0006 • rebate_organization (组织表) ← ZRET0014</text>

              <rect x="1140" y="530" width="380" height="70" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1330" y="550" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">双写机制技术实现</text>
              <text x="1150" y="570" fontSize="9" fill="white">• 实时双写：SAP HANA + MySQL 同步写入 • 数据校验：MD5哈希值比对</text>
              <text x="1150" y="585" fontSize="9" fill="white">• 补偿事务：Saga模式 + 自动修复机制 • 一致性保障：定时校验任务</text>

              {/* Migration Benefits */}
              <rect x="740" y="630" width="790" height="90" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1135" y="650" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">一期改造收益</text>

              <rect x="760" y="670" width="180" height="40" rx="6" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="1"/>
              <text x="850" y="685" textAnchor="middle" fontSize="10" fontWeight="bold" fill="white">用户体验提升</text>
              <text x="770" y="700" fontSize="8" fill="white">响应时间: 3-5秒 → &lt;500ms</text>

              <rect x="960" y="670" width="180" height="40" rx="6" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" strokeWidth="1"/>
              <text x="1050" y="685" textAnchor="middle" fontSize="10" fontWeight="bold" fill="white">开发效率提升</text>
              <text x="970" y="700" fontSize="8" fill="white">ABAP → Java/Amis 80%效率提升</text>

              <rect x="1160" y="670" width="180" height="40" rx="6" fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" strokeWidth="1"/>
              <text x="1250" y="685" textAnchor="middle" fontSize="10" fontWeight="bold" fill="white">运维成本降低</text>
              <text x="1170" y="700" fontSize="8" fill="white">容器化部署 + 自动化运维</text>

              <rect x="1360" y="670" width="160" height="40" rx="6" fill="rgba(168, 85, 247, 0.2)" stroke="#a855f7" strokeWidth="1"/>
              <text x="1440" y="685" textAnchor="middle" fontSize="10" fontWeight="bold" fill="white">扩展性增强</text>
              <text x="1370" y="700" fontSize="8" fill="white">微服务架构 + 弹性扩容</text>

              {/* Phase 1 Scope */}
              <rect x="740" y="740" width="790" height="60" rx="8" fill="rgba(255,255,255,0.1)" stroke="white" strokeWidth="1"/>
              <text x="1135" y="760" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#fbbf24">一期改造核心策略</text>
              <text x="750" y="780" fontSize="10" fill="white">✅ 保持SAP核心表结构不变 ✅ 构建现代化管理界面 ✅ 建立双写数据同步机制 ✅ 实现业务逻辑微服务化</text>
              <text x="750" y="795" fontSize="10" fill="white">✅ 支持双系统并行运行 ✅ 数据一致性实时校验 ✅ 为后续完全迁移奠定基础 ✅ 零业务中断平滑切换</text>
            </g>

            {/* Data Flow Arrows */}
            <path d="M 400 450 L 420 450" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#arrowhead)"/>
            <text x="410" y="440" textAnchor="middle" fontSize="10" fontWeight="bold" fill="#0891b2">实时触发</text>

            <path d="M 700 450 L 720 450" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#arrowhead)"/>
            <text x="710" y="440" textAnchor="middle" fontSize="10" fontWeight="bold" fill="#0891b2">异步同步</text>

            {/* Bidirectional Sync */}
            <path d="M 560 820 Q 560 850 225 850 Q 225 820 225 800" stroke="#f59e0b" strokeWidth="3" fill="none" markerEnd="url(#arrowhead)"/>
            <text x="390" y="870" textAnchor="middle" fontSize="10" fontWeight="bold" fill="#f59e0b">双向数据校验与补偿</text>

            {/* Evolution Arrows */}
            <path d="M 400 300 Q 500 250 600 300" stroke="#7c3aed" strokeWidth="3" fill="none" markerEnd="url(#evolutionArrow)"/>
            <text x="500" y="270" textAnchor="middle" fontSize="10" fontWeight="bold" fill="#7c3aed">渐进式演进</text>

            <path d="M 700 300 Q 800 250 900 300" stroke="#7c3aed" strokeWidth="3" fill="none" markerEnd="url(#evolutionArrow)"/>
            <text x="800" y="270" textAnchor="middle" fontSize="10" fontWeight="bold" fill="#7c3aed">能力构建</text>

            {/* Future Evolution Path */}
            <g id="futureEvolution">
              <rect x="50" y="830" width="1500" height="120" rx="8" fill="#1f2937" stroke="#374151" strokeWidth="2"/>
              <text x="800" y="850" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#f8fafc">技术架构演进路线图</text>

              {/* Phase Timeline */}
              <rect x="80" y="870" width="200" height="60" rx="6" fill="rgba(220, 38, 38, 0.2)" stroke="#dc2626" strokeWidth="1"/>
              <text x="180" y="885" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#dc2626">现状 (SAP+HANA)</text>
              <text x="90" y="900" fontSize="9" fill="white">• 传统ABAP开发</text>
              <text x="90" y="915" fontSize="9" fill="white">• GUI界面体验差</text>
              <text x="90" y="925" fontSize="9" fill="white">• 扩展性受限</text>

              <rect x="300" y="870" width="200" height="60" rx="6" fill="rgba(8, 145, 178, 0.2)" stroke="#0891b2" strokeWidth="1"/>
              <text x="400" y="885" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#0891b2">一期 (双写并行)</text>
              <text x="310" y="900" fontSize="9" fill="white">• 现代化界面</text>
              <text x="310" y="915" fontSize="9" fill="white">• 双数据库同步</text>
              <text x="310" y="925" fontSize="9" fill="white">• 微服务基础</text>

              <rect x="520" y="870" width="200" height="60" rx="6" fill="rgba(5, 150, 105, 0.2)" stroke="#059669" strokeWidth="1"/>
              <text x="620" y="885" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#059669">二期 (计算引擎)</text>
              <text x="530" y="900" fontSize="9" fill="white">• 返利计算迁移</text>
              <text x="530" y="915" fontSize="9" fill="white">• 业务规则引擎</text>
              <text x="530" y="925" fontSize="9" fill="white">• 性能优化</text>

              <rect x="740" y="870" width="200" height="60" rx="6" fill="rgba(124, 58, 237, 0.2)" stroke="#7c3aed" strokeWidth="1"/>
              <text x="840" y="885" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#7c3aed">三期 (结算报表)</text>
              <text x="750" y="900" fontSize="9" fill="white">• 结算模块迁移</text>
              <text x="750" y="915" fontSize="9" fill="white">• 报表系统重构</text>
              <text x="750" y="925" fontSize="9" fill="white">• 数据分析能力</text>

              <rect x="960" y="870" width="200" height="60" rx="6" fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" strokeWidth="1"/>
              <text x="1060" y="885" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#f59e0b">四期 (完全替换)</text>
              <text x="970" y="900" fontSize="9" fill="white">• SAP系统下线</text>
              <text x="970" y="915" fontSize="9" fill="white">• 云原生架构</text>
              <text x="970" y="925" fontSize="9" fill="white">• 智能化运维</text>

              <rect x="1180" y="870" width="360" height="60" rx="6" fill="rgba(55, 65, 81, 0.5)" stroke="#6b7280" strokeWidth="1"/>
              <text x="1360" y="885" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#f8fafc">风险控制策略</text>
              <text x="1190" y="900" fontSize="9" fill="white">• 双系统并行运行 • 数据一致性保障 • 灰度发布机制</text>
              <text x="1190" y="915" fontSize="9" fill="white">• 快速回滚能力 • 实时监控告警 • 应急响应预案</text>
              <text x="1190" y="925" fontSize="9" fill="white">• 零业务中断 • 用户无感知切换 • 业务连续性保障</text>
            </g>
          </svg>
        </div>
      </div>
    </div>
  )
}
