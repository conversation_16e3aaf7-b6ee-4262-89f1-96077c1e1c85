# 返利系统重构技术架构方案

## 1. 项目背景与目标

### 1.1 现状分析
基于对现有SAP返利系统的深度分析，当前系统存在以下特点：
- **SAP ABAP技术栈**：ZRED0040(合同)、ZRED0041(条款)、ZRED0056(协议)等核心模块
- **集中式架构**：所有业务逻辑集中在SAP系统中
- **数据库依赖**：完全依赖SAP HANA数据库
- **界面局限**：传统SAP GUI界面，用户体验有限

### 1.2 改造目标
- **技术现代化**：从SAP ABAP迁移到Spring Boot微服务架构
- **用户体验提升**：现代化Web界面替代传统SAP GUI
- **系统解耦**：逐步将业务逻辑从SAP中剥离
- **数据安全**：建立异构数据库双写机制，确保数据安全

### 1.3 改造策略
采用**分期渐进式改造**策略，确保业务连续性：
- **一期**：合同、条款、协议管理模块迁移
- **二期**：返利计算引擎迁移
- **三期**：结算和报表模块迁移
- **四期**：完全替换SAP返利系统

## 2. 全局多期整体改造方案

### 2.1 整体架构演进路线图

```mermaid
graph TB
    subgraph "当前架构 (现状)"
        A1[SAP GUI界面]
        A2[SAP ABAP业务逻辑]
        A3[SAP HANA数据库]
        A4[BDP数据平台]
        A5[外部系统集成]
        
        A1 --> A2
        A2 --> A3
        A2 --> A4
        A2 --> A5
    end
    
    subgraph "一期架构 (合同管理)"
        B1[现代化Web界面]
        B2[Spring Boot微服务]
        B3[SAP HANA数据库]
        B4[微服务数据库]
        B5[数据同步服务]
        B6[SAP ABAP剩余模块]
        
        B1 --> B2
        B2 --> B3
        B2 --> B4
        B3 --> B5
        B5 --> B4
        B6 --> B3
    end
    
    subgraph "二期架构 (计算引擎)"
        C1[Web界面扩展]
        C2[返利计算微服务]
        C3[合同管理微服务]
        C4[微服务数据库集群]
        C5[SAP ABAP最小化]
        
        C1 --> C2
        C1 --> C3
        C2 --> C4
        C3 --> C4
        C5 --> C4
    end
    
    subgraph "目标架构 (完全微服务)"
        D1[统一前端平台]
        D2[API网关]
        D3[微服务集群]
        D4[分布式数据库]
        D5[消息队列]
        D6[监控运维平台]
        
        D1 --> D2
        D2 --> D3
        D3 --> D4
        D3 --> D5
        D6 --> D3
    end
    
    A1 -.-> B1
    A2 -.-> B2
    A3 -.-> B3
    B2 -.-> C2
    B2 -.-> C3
    C2 -.-> D3
    C3 -.-> D3
```

### 2.2 分期改造详细规划

#### 2.2.1 一期：合同管理模块 (3-4个月)
**改造范围**：
- 合同创建/修改/查询 (ZRED0040A/B/C)
- 条款管理 (ZRED0041A)
- 协议管理 (ZRED0056)
- 审批流程

**技术目标**：
- Spring Boot微服务架构
- 现代化Web界面
- 双数据库写入
- 数据同步机制

#### 2.2.2 二期：返利计算引擎 (4-6个月)
**改造范围**：
- 返利计算逻辑
- 数据分摊处理
- 批量处理功能
- 算法配置管理

**技术目标**：
- 高性能计算引擎
- 分布式计算能力
- 实时计算支持
- 算法可配置化

#### 2.2.3 三期：结算报表模块 (3-4个月)
**改造范围**：
- 结算单管理
- 报表生成
- 数据导出
- 外部系统集成

**技术目标**：
- 高并发报表服务
- 实时数据展示
- 多格式导出
- API标准化

#### 2.2.4 四期：系统整合优化 (2-3个月)
**改造范围**：
- SAP系统下线
- 数据迁移完成
- 性能优化
- 运维体系建设

**技术目标**：
- 完全微服务化
- 云原生架构
- DevOps体系
- 智能运维

### 2.3 技术栈选型

| 层次 | 当前技术 | 目标技术 | 说明 |
|------|---------|---------|------|
| **前端** | SAP GUI | Vue.js 3 + Element Plus | 现代化Web界面 |
| **网关** | 无 | Spring Cloud Gateway | API网关和路由 |
| **后端** | SAP ABAP | Spring Boot 3 + Spring Cloud | 微服务架构 |
| **数据库** | SAP HANA | MySQL 8.0 + Redis | 关系型+缓存 |
| **消息队列** | 无 | RabbitMQ / Apache Kafka | 异步处理 |
| **配置中心** | SAP配置表 | Nacos / Spring Cloud Config | 配置管理 |
| **注册中心** | 无 | Nacos / Eureka | 服务发现 |
| **监控** | SAP监控 | Prometheus + Grafana | 系统监控 |
| **日志** | SAP日志 | ELK Stack | 日志分析 |
| **部署** | SAP服务器 | Docker + Kubernetes | 容器化部署 |

## 3. 一期合同管理改造技术方案

### 3.1 一期架构设计

```mermaid
graph TB
    subgraph "前端层"
        F1[Vue.js 3前端应用]
        F2[Element Plus UI组件]
        F3[Axios HTTP客户端]
    end
    
    subgraph "网关层"
        G1[Spring Cloud Gateway]
        G2[认证授权模块]
        G3[限流熔断]
    end
    
    subgraph "微服务层"
        M1[合同管理服务<br/>contract-service]
        M2[条款管理服务<br/>clause-service]
        M3[协议管理服务<br/>agreement-service]
        M4[审批流程服务<br/>approval-service]
        M5[用户权限服务<br/>auth-service]
        M6[数据同步服务<br/>sync-service]
    end
    
    subgraph "数据层"
        D1[SAP HANA数据库<br/>主数据库]
        D2[MySQL数据库<br/>微服务数据库]
        D3[Redis缓存]
        D4[数据同步中间件]
    end
    
    subgraph "外部系统"
        E1[SAP ABAP剩余模块]
        E2[BDP数据平台]
        E3[审批工作流引擎]
    end
    
    F1 --> F2
    F1 --> F3
    F3 --> G1
    G1 --> G2
    G1 --> G3
    G2 --> M1
    G2 --> M2
    G2 --> M3
    G2 --> M4
    G2 --> M5
    
    M1 --> D1
    M1 --> D2
    M2 --> D1
    M2 --> D2
    M3 --> D1
    M3 --> D2
    M4 --> D3
    M5 --> D3
    
    M6 --> D1
    M6 --> D2
    M6 --> D4
    
    M4 --> E3
    M1 --> E1
    M2 --> E1
    M3 --> E1
```

### 3.2 核心微服务设计

#### 3.2.1 合同管理服务 (contract-service)
**功能职责**：
- 合同CRUD操作
- 合同状态管理
- 合同审批流程
- 商品组管理

**核心API设计**：
```java
@RestController
@RequestMapping("/api/v1/contracts")
public class ContractController {
    
    @PostMapping
    public ResponseEntity<ContractDTO> createContract(@RequestBody ContractCreateRequest request);
    
    @PutMapping("/{contractId}")
    public ResponseEntity<ContractDTO> updateContract(@PathVariable String contractId, 
                                                     @RequestBody ContractUpdateRequest request);
    
    @GetMapping("/{contractId}")
    public ResponseEntity<ContractDTO> getContract(@PathVariable String contractId);
    
    @GetMapping
    public ResponseEntity<PageResult<ContractDTO>> getContracts(@RequestParam ContractQueryRequest request);
    
    @PostMapping("/{contractId}/submit")
    public ResponseEntity<Void> submitForApproval(@PathVariable String contractId);
}
```

#### 3.2.2 数据同步服务 (sync-service)
**功能职责**：
- SAP HANA到MySQL的数据同步
- 数据一致性校验
- 同步状态监控
- 异常处理和重试

**同步策略**：
```java
@Service
public class DataSyncService {
    
    @Async
    public void syncContractData(String contractId, SyncOperation operation) {
        try {
            // 1. 从SAP HANA读取数据
            ContractEntity sapData = sapHanaRepository.findById(contractId);
            
            // 2. 转换数据格式
            ContractEntity mysqlData = dataConverter.convert(sapData);
            
            // 3. 写入MySQL数据库
            mysqlRepository.save(mysqlData);
            
            // 4. 记录同步日志
            syncLogService.recordSuccess(contractId, operation);
            
        } catch (Exception e) {
            // 异常处理和重试机制
            syncLogService.recordFailure(contractId, operation, e);
            retryService.scheduleRetry(contractId, operation);
        }
    }
}
```

### 3.3 数据库设计方案

#### 3.3.1 异构数据库映射关系

| SAP HANA表 | MySQL表 | 映射说明 |
|-----------|---------|---------|
| ZRET0001 | rebate_contract | 合同主表 |
| ZRET0002 | rebate_clause | 条款主表 |
| ZRET0006 | rebate_agreement | 协议主表 |
| ZRET0003 | rebate_product_group | 商品组表 |
| ZRET0004 | rebate_product_group_item | 商品组明细表 |

#### 3.3.2 MySQL数据库表结构设计

```sql
-- 合同主表
CREATE TABLE rebate_contract (
    id VARCHAR(20) PRIMARY KEY COMMENT '合同编号',
    contract_type VARCHAR(10) NOT NULL COMMENT '合同类型',
    contract_subject VARCHAR(10) NOT NULL COMMENT '合同主体',
    partner_type VARCHAR(1) NOT NULL COMMENT '伙伴类型',
    partner_code VARCHAR(20) NOT NULL COMMENT '伙伴编码',
    purchase_group VARCHAR(10) COMMENT '采购组',
    contract_year VARCHAR(4) NOT NULL COMMENT '签署年度',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    contract_name VARCHAR(100) COMMENT '合同名称',
    settlement_cycle VARCHAR(10) COMMENT '结算周期',
    payment_party VARCHAR(20) COMMENT '支付方',
    payment_cycle VARCHAR(10) COMMENT '付款周期',
    status VARCHAR(2) DEFAULT '01' COMMENT '状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    INDEX idx_partner (partner_type, partner_code),
    INDEX idx_date (start_date, end_date),
    INDEX idx_status (status),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利合同表';

-- 条款主表
CREATE TABLE rebate_clause (
    id VARCHAR(20) PRIMARY KEY COMMENT '条款编号',
    contract_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    clause_desc VARCHAR(200) NOT NULL COMMENT '条款描述',
    rebate_type VARCHAR(10) NOT NULL COMMENT '返利类型',
    calculation_base VARCHAR(10) NOT NULL COMMENT '核算基准',
    rebate_form VARCHAR(10) NOT NULL COMMENT '返利形式',
    quantitative_dimension VARCHAR(10) COMMENT '量化维度',
    rebate_rule VARCHAR(10) COMMENT '返利规则',
    ladder_type VARCHAR(10) COMMENT '阶梯类型',
    price_dimension VARCHAR(10) COMMENT '返利价格维度',
    calculation_method VARCHAR(10) COMMENT '计算方法',
    external_supplier VARCHAR(20) COMMENT '外部供货商',
    external_payer VARCHAR(20) COMMENT '外部支付方',
    product_group_id VARCHAR(20) COMMENT '商品组ID',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    status VARCHAR(2) DEFAULT '01' COMMENT '状态',
    approval_status VARCHAR(2) DEFAULT '01' COMMENT '审批状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    FOREIGN KEY (contract_id) REFERENCES rebate_contract(id),
    INDEX idx_contract (contract_id),
    INDEX idx_type (rebate_type),
    INDEX idx_status (status, approval_status),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利条款表';

-- 协议主表
CREATE TABLE rebate_agreement (
    id VARCHAR(20) PRIMARY KEY COMMENT '协议编号',
    clause_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    organization_level VARCHAR(1) NOT NULL COMMENT '组织级别',
    agreement_subject VARCHAR(10) NOT NULL COMMENT '协议主体',
    payer VARCHAR(20) NOT NULL COMMENT '付款方',
    payer_level VARCHAR(1) NOT NULL COMMENT '付款方级别',
    exchange_method VARCHAR(10) NOT NULL COMMENT '兑换方式',
    exclusive_flag VARCHAR(1) DEFAULT 'N' COMMENT '专属标识',
    payment_type VARCHAR(10) COMMENT '付款类型',
    agreement_amount DECIMAL(15,2) COMMENT '协议金额',
    agreement_type VARCHAR(1) DEFAULT 'F' COMMENT '协议类型',
    status VARCHAR(2) DEFAULT '01' COMMENT '状态',
    approval_status VARCHAR(2) DEFAULT '01' COMMENT '审批状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    FOREIGN KEY (clause_id) REFERENCES rebate_clause(id),
    INDEX idx_clause (clause_id),
    INDEX idx_payer (payer, payer_level),
    INDEX idx_status (status, approval_status),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利协议表';
```

#### 3.3.3 数据同步触发器设计

```sql
-- SAP HANA触发器：监控数据变更并通知微服务
CREATE TRIGGER tr_contract_sync
AFTER INSERT OR UPDATE OR DELETE ON ZRET0001
FOR EACH ROW
BEGIN
    DECLARE sync_operation VARCHAR(10);
    DECLARE contract_data CLOB;

    -- 确定操作类型
    IF INSERTING THEN
        sync_operation := 'INSERT';
        contract_data := TO_CLOB(:NEW);
    ELSIF UPDATING THEN
        sync_operation := 'UPDATE';
        contract_data := TO_CLOB(:NEW);
    ELSE
        sync_operation := 'DELETE';
        contract_data := TO_CLOB(:OLD);
    END IF;

    -- 插入同步队列表
    INSERT INTO ZSYNC_QUEUE (
        table_name,
        operation_type,
        record_id,
        data_content,
        sync_status,
        created_time
    ) VALUES (
        'ZRET0001',
        sync_operation,
        COALESCE(:NEW.ZHT_ID, :OLD.ZHT_ID),
        contract_data,
        'PENDING',
        CURRENT_TIMESTAMP
    );
END;
```

### 3.4 微服务详细设计

#### 3.4.1 合同管理服务架构

```mermaid
graph TB
    subgraph "合同管理服务 (contract-service)"
        C1[Controller层]
        C2[Service层]
        C3[Repository层]
        C4[Entity层]
        C5[DTO层]
        C6[Validation层]
        C7[Exception处理层]

        C1 --> C2
        C2 --> C3
        C3 --> C4
        C1 --> C5
        C1 --> C6
        C1 --> C7
    end

    subgraph "数据访问层"
        D1[SAP HANA Repository]
        D2[MySQL Repository]
        D3[Redis Cache]
    end

    subgraph "外部依赖"
        E1[审批服务]
        E2[用户服务]
        E3[数据同步服务]
    end

    C3 --> D1
    C3 --> D2
    C3 --> D3
    C2 --> E1
    C2 --> E2
    C2 --> E3
```

#### 3.4.2 核心业务逻辑实现

```java
@Service
@Transactional
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractSapRepository sapRepository;

    @Autowired
    private ContractMysqlRepository mysqlRepository;

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private ApprovalService approvalService;

    @Override
    public ContractDTO createContract(ContractCreateRequest request) {
        // 1. 数据验证
        validateContractData(request);

        // 2. 生成合同编号
        String contractId = generateContractId();

        // 3. 构建合同实体
        ContractEntity contract = buildContractEntity(contractId, request);

        // 4. 双写数据库
        try {
            // 主库写入 (SAP HANA)
            ContractEntity sapEntity = sapRepository.save(contract);

            // 备库写入 (MySQL)
            ContractEntity mysqlEntity = convertToMysqlEntity(sapEntity);
            mysqlRepository.save(mysqlEntity);

            // 5. 异步同步确认
            dataSyncService.confirmSync(contractId, "CREATE");

            return convertToDTO(sapEntity);

        } catch (Exception e) {
            // 回滚和异常处理
            handleCreateException(contractId, e);
            throw new BusinessException("合同创建失败", e);
        }
    }

    @Override
    public ContractDTO updateContract(String contractId, ContractUpdateRequest request) {
        // 1. 检查合同存在性和状态
        ContractEntity existingContract = sapRepository.findById(contractId)
            .orElseThrow(() -> new BusinessException("合同不存在"));

        if (!canModifyContract(existingContract)) {
            throw new BusinessException("合同当前状态不允许修改");
        }

        // 2. 数据验证
        validateUpdateData(request, existingContract);

        // 3. 更新合同数据
        updateContractFields(existingContract, request);

        // 4. 双写更新
        try {
            // 主库更新
            ContractEntity updatedSapEntity = sapRepository.save(existingContract);

            // 备库更新
            ContractEntity mysqlEntity = convertToMysqlEntity(updatedSapEntity);
            mysqlRepository.save(mysqlEntity);

            // 5. 异步同步确认
            dataSyncService.confirmSync(contractId, "UPDATE");

            return convertToDTO(updatedSapEntity);

        } catch (Exception e) {
            handleUpdateException(contractId, e);
            throw new BusinessException("合同更新失败", e);
        }
    }

    private void validateContractData(ContractCreateRequest request) {
        // 业务规则验证，对应SAP中的验证逻辑

        // 1. 必填字段验证
        if (StringUtils.isEmpty(request.getPartnerCode())) {
            throw new ValidationException("伙伴编码不能为空");
        }

        // 2. 伙伴代码验证 (对应SAP中的frm_check_partner)
        if (!isValidPartner(request.getPartnerType(), request.getPartnerCode())) {
            throw new ValidationException("伙伴不存在");
        }

        // 3. 日期逻辑验证
        if (request.getStartDate().after(request.getEndDate())) {
            throw new ValidationException("开始日期不能大于结束日期");
        }

        // 4. 合同重复性检查
        if (isDuplicateContract(request)) {
            throw new ValidationException("存在重复的合同");
        }
    }
}
```

### 3.5 前端架构设计

#### 3.5.1 前端技术栈

```mermaid
graph TB
    subgraph "前端架构"
        F1[Vue.js 3]
        F2[Vue Router 4]
        F3[Pinia状态管理]
        F4[Element Plus UI]
        F5[Axios HTTP客户端]
        F6[TypeScript]
        F7[Vite构建工具]
    end

    subgraph "页面组件"
        P1[合同列表页面]
        P2[合同创建页面]
        P3[合同编辑页面]
        P4[条款管理页面]
        P5[协议管理页面]
        P6[审批流程页面]
    end

    subgraph "公共组件"
        C1[表格组件]
        C2[表单组件]
        C3[搜索组件]
        C4[分页组件]
        C5[上传组件]
        C6[审批组件]
    end

    F1 --> P1
    F1 --> P2
    F1 --> P3
    F1 --> P4
    F1 --> P5
    F1 --> P6

    P1 --> C1
    P2 --> C2
    P3 --> C2
    P4 --> C3
    P5 --> C4
    P6 --> C6
```

#### 3.5.2 核心页面设计

```vue
<!-- 合同管理主页面 -->
<template>
  <div class="contract-management">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="合同编号">
          <el-input v-model="searchForm.contractId" placeholder="请输入合同编号" />
        </el-form-item>
        <el-form-item label="伙伴编码">
          <el-input v-model="searchForm.partnerCode" placeholder="请输入伙伴编码" />
        </el-form-item>
        <el-form-item label="合同状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="草稿" value="01" />
            <el-option label="待审批" value="02" />
            <el-option label="已审批" value="03" />
            <el-option label="已拒绝" value="04" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card">
      <el-button type="primary" @click="handleCreate">新建合同</el-button>
      <el-button type="success" @click="handleBatchApproval">批量审批</el-button>
      <el-button type="info" @click="handleExport">导出</el-button>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="contractList" v-loading="loading">
        <el-table-column prop="id" label="合同编号" width="150" />
        <el-table-column prop="contractName" label="合同名称" width="200" />
        <el-table-column prop="partnerCode" label="伙伴编码" width="120" />
        <el-table-column prop="contractType" label="合同类型" width="100" />
        <el-table-column prop="startDate" label="开始日期" width="120" />
        <el-table-column prop="endDate" label="结束日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)"
                       :disabled="!canEdit(row)">编辑</el-button>
            <el-button size="small" type="success" @click="handleSubmit(row)"
                       :disabled="!canSubmit(row)">提交审批</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { contractApi } from '@/api/contract'
import type { ContractDTO, ContractQueryRequest } from '@/types/contract'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const contractList = ref<ContractDTO[]>([])

const searchForm = reactive<ContractQueryRequest>({
  contractId: '',
  partnerCode: '',
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 方法定义
const handleSearch = async () => {
  loading.value = true
  try {
    const response = await contractApi.getContracts({
      ...searchForm,
      page: pagination.current,
      size: pagination.size
    })
    contractList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  router.push('/contract/create')
}

const handleEdit = (row: ContractDTO) => {
  router.push(`/contract/edit/${row.id}`)
}

const handleSubmit = async (row: ContractDTO) => {
  try {
    await ElMessageBox.confirm('确认提交该合同进行审批？', '提示', {
      type: 'warning'
    })

    await contractApi.submitForApproval(row.id)
    ElMessage.success('提交成功')
    handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败')
    }
  }
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>
```

### 3.6 部署架构设计

#### 3.6.1 容器化部署架构

```mermaid
graph TB
    subgraph "Kubernetes集群"
        subgraph "Ingress层"
            I1[Nginx Ingress Controller]
        end

        subgraph "应用层"
            A1[Gateway Pod]
            A2[Contract Service Pod]
            A3[Clause Service Pod]
            A4[Agreement Service Pod]
            A5[Sync Service Pod]
            A6[Auth Service Pod]
        end

        subgraph "配置层"
            C1[ConfigMap]
            C2[Secret]
            C3[Nacos Config]
        end

        subgraph "存储层"
            S1[MySQL PVC]
            S2[Redis PVC]
            S3[Log PVC]
        end
    end

    subgraph "外部系统"
        E1[SAP HANA数据库]
        E2[前端应用]
        E3[监控系统]
    end

    E2 --> I1
    I1 --> A1
    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5
    A1 --> A6

    A2 --> C1
    A2 --> C2
    A2 --> C3
    A2 --> S1
    A2 --> S2
    A2 --> E1

    A5 --> E1
    A6 --> S2

    A1 --> E3
```

#### 3.6.2 Docker容器配置

```dockerfile
# 微服务基础镜像
FROM openjdk:17-jdk-slim

# 设置工作目录
WORKDIR /app

# 复制应用JAR文件
COPY target/contract-service-1.0.0.jar app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 3.6.3 Kubernetes部署配置

```yaml
# contract-service部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: contract-service
  namespace: rebate-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: contract-service
  template:
    metadata:
      labels:
        app: contract-service
    spec:
      containers:
      - name: contract-service
        image: rebate/contract-service:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MYSQL_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: mysql-host
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: mysql-password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
# Service配置
apiVersion: v1
kind: Service
metadata:
  name: contract-service
  namespace: rebate-system
spec:
  selector:
    app: contract-service
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
```

### 3.7 数据迁移和同步策略

#### 3.7.1 数据迁移方案

```mermaid
sequenceDiagram
    participant Admin as 系统管理员
    participant Migration as 迁移工具
    participant SAP as SAP HANA
    participant MySQL as MySQL数据库
    participant Validation as 数据校验

    Admin->>Migration: 启动数据迁移
    Migration->>SAP: 读取历史数据
    SAP-->>Migration: 返回数据集
    Migration->>Migration: 数据转换和清洗
    Migration->>MySQL: 批量写入数据
    MySQL-->>Migration: 写入确认
    Migration->>Validation: 启动数据校验
    Validation->>SAP: 查询源数据
    Validation->>MySQL: 查询目标数据
    Validation->>Validation: 对比数据一致性
    Validation-->>Admin: 校验报告
```

#### 3.7.2 实时同步机制

```java
@Component
public class RealTimeSyncProcessor {

    @Autowired
    private SyncQueueRepository syncQueueRepository;

    @Autowired
    private ContractMysqlRepository contractMysqlRepository;

    @Scheduled(fixedDelay = 5000) // 每5秒执行一次
    public void processSyncQueue() {
        List<SyncQueueEntity> pendingItems = syncQueueRepository
            .findByStatusOrderByCreatedTime("PENDING", PageRequest.of(0, 100));

        for (SyncQueueEntity item : pendingItems) {
            try {
                processSyncItem(item);
                item.setStatus("SUCCESS");
                item.setProcessedTime(LocalDateTime.now());
            } catch (Exception e) {
                item.setStatus("FAILED");
                item.setErrorMessage(e.getMessage());
                item.setRetryCount(item.getRetryCount() + 1);

                // 重试机制
                if (item.getRetryCount() < 3) {
                    item.setStatus("PENDING");
                    item.setNextRetryTime(LocalDateTime.now().plusMinutes(5));
                }
            } finally {
                syncQueueRepository.save(item);
            }
        }
    }

    private void processSyncItem(SyncQueueEntity item) {
        switch (item.getOperationType()) {
            case "INSERT":
                handleInsert(item);
                break;
            case "UPDATE":
                handleUpdate(item);
                break;
            case "DELETE":
                handleDelete(item);
                break;
        }
    }

    private void handleInsert(SyncQueueEntity item) {
        // 解析SAP数据并转换为MySQL格式
        ContractEntity mysqlEntity = parseAndConvert(item.getDataContent());
        contractMysqlRepository.save(mysqlEntity);
    }
}
```

### 3.8 监控和运维体系

#### 3.8.1 监控架构

```mermaid
graph TB
    subgraph "应用监控"
        M1[Spring Boot Actuator]
        M2[Micrometer Metrics]
        M3[Custom Metrics]
    end

    subgraph "基础设施监控"
        I1[Kubernetes Metrics]
        I2[Docker Stats]
        I3[Node Exporter]
    end

    subgraph "数据收集"
        C1[Prometheus]
        C2[Grafana]
        C3[AlertManager]
    end

    subgraph "日志系统"
        L1[Logback]
        L2[Filebeat]
        L3[Elasticsearch]
        L4[Kibana]
    end

    M1 --> C1
    M2 --> C1
    M3 --> C1
    I1 --> C1
    I2 --> C1
    I3 --> C1

    C1 --> C2
    C1 --> C3

    L1 --> L2
    L2 --> L3
    L3 --> L4
```

#### 3.8.2 关键监控指标

| 监控类型 | 监控指标 | 告警阈值 | 处理方式 |
|---------|---------|---------|---------|
| **应用性能** | 响应时间 | >2秒 | 自动扩容 |
| **应用性能** | 错误率 | >5% | 立即告警 |
| **应用性能** | QPS | >1000 | 限流保护 |
| **数据同步** | 同步延迟 | >30秒 | 检查同步服务 |
| **数据同步** | 失败率 | >1% | 人工介入 |
| **系统资源** | CPU使用率 | >80% | 自动扩容 |
| **系统资源** | 内存使用率 | >85% | 自动扩容 |
| **数据库** | 连接数 | >80% | 连接池调优 |

## 4. 实施计划和风险控制

### 4.1 一期实施计划 (12周)

#### 第1-2周：环境准备和基础设施搭建
- [ ] Kubernetes集群搭建
- [ ] MySQL数据库部署
- [ ] Redis缓存部署
- [ ] 监控系统部署
- [ ] CI/CD流水线搭建

#### 第3-4周：数据库设计和数据迁移
- [ ] MySQL数据库表结构设计
- [ ] 数据迁移工具开发
- [ ] 历史数据迁移
- [ ] 数据一致性校验

#### 第5-6周：后端微服务开发
- [ ] 合同管理服务开发
- [ ] 条款管理服务开发
- [ ] 协议管理服务开发
- [ ] 数据同步服务开发

#### 第7-8周：前端页面开发
- [ ] 合同管理页面开发
- [ ] 条款管理页面开发
- [ ] 协议管理页面开发
- [ ] 审批流程页面开发

#### 第9-10周：系统集成和测试
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能测试
- [ ] 安全测试

#### 第11-12周：部署上线和验收
- [ ] 生产环境部署
- [ ] 用户培训
- [ ] 系统验收
- [ ] 文档交付

### 4.2 风险控制措施

#### 4.2.1 技术风险
| 风险项 | 风险等级 | 应对措施 |
|-------|---------|---------|
| 数据同步延迟 | 高 | 实时监控+告警机制 |
| 数据一致性问题 | 高 | 双写+校验+补偿机制 |
| 性能瓶颈 | 中 | 压力测试+性能调优 |
| 系统兼容性 | 中 | 充分测试+灰度发布 |

#### 4.2.2 业务风险
| 风险项 | 风险等级 | 应对措施 |
|-------|---------|---------|
| 业务中断 | 高 | 灰度发布+快速回滚 |
| 数据丢失 | 高 | 数据备份+恢复机制 |
| 用户接受度 | 中 | 用户培训+操作指南 |
| 功能缺失 | 中 | 需求确认+功能测试 |

### 4.3 成功标准

#### 4.3.1 技术指标
- 系统响应时间 < 2秒
- 系统可用性 > 99.9%
- 数据同步延迟 < 10秒
- 数据一致性 > 99.99%

#### 4.3.2 业务指标
- 用户操作效率提升 > 50%
- 界面满意度 > 90%
- 功能完整性 = 100%
- 培训通过率 > 95%

## 5. 总结

本技术架构方案采用分期渐进式改造策略，确保业务连续性的同时实现技术升级。一期重点解决合同管理模块的现代化改造，建立双数据库架构和实时同步机制，为后续的全面微服务化奠定基础。

通过Spring Boot微服务架构、Vue.js现代化前端、Kubernetes容器化部署等技术栈，将显著提升系统的可维护性、扩展性和用户体验，为企业数字化转型提供强有力的技术支撑。
