export default function Diagram5() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-500 rounded"></div>
            <h1 className="text-4xl font-bold text-white">返利引擎四期改造战略架构</h1>
            <div className="px-4 py-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-full text-white text-sm font-semibold">
              Strategic Roadmap
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold text-lg">商济健康</div>
              <div className="text-red-300 text-sm">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Strategic Overview */}
        <div className="mb-8 p-6 bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-2xl border border-blue-500/30 backdrop-blur-sm">
          <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
            <span className="text-3xl">🎯</span>
            战略改造目标
          </h2>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center p-4 bg-white/10 rounded-xl backdrop-blur-sm">
              <div className="text-3xl mb-2">🚀</div>
              <div className="text-white font-semibold">技术现代化</div>
              <div className="text-blue-300 text-sm">SAP ABAP → 微服务</div>
            </div>
            <div className="text-center p-4 bg-white/10 rounded-xl backdrop-blur-sm">
              <div className="text-3xl mb-2">💡</div>
              <div className="text-white font-semibold">业务智能化</div>
              <div className="text-blue-300 text-sm">AI算法 + 精准计算</div>
            </div>
            <div className="text-center p-4 bg-white/10 rounded-xl backdrop-blur-sm">
              <div className="text-3xl mb-2">⚡</div>
              <div className="text-white font-semibold">响应敏捷化</div>
              <div className="text-blue-300 text-sm">秒级响应 + 实时决策</div>
            </div>
            <div className="text-center p-4 bg-white/10 rounded-xl backdrop-blur-sm">
              <div className="text-3xl mb-2">🔧</div>
              <div className="text-white font-semibold">运维自动化</div>
              <div className="text-blue-300 text-sm">DevOps + 成本降60%</div>
            </div>
          </div>
        </div>

        {/* Four Phase Architecture */}
        <div className="space-y-8">
          {/* Current State */}
          <div className="relative">
            <div className="absolute left-0 top-0 w-2 h-full bg-gradient-to-b from-red-500 to-red-700 rounded-full"></div>
            <div className="ml-8 p-6 bg-gradient-to-r from-red-900/30 to-red-800/30 rounded-2xl border border-red-500/30">
              <h3 className="text-xl font-bold text-red-300 mb-4 flex items-center gap-2">
                <span className="px-3 py-1 bg-red-500 text-white rounded-full text-sm">现状</span>
                SAP集中式架构
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="p-4 bg-red-800/20 rounded-xl border border-red-500/20">
                  <div className="text-red-300 font-semibold mb-2">📱 SAP GUI界面</div>
                  <div className="text-red-200 text-sm space-y-1">
                    <div>• 传统界面，响应缓慢</div>
                    <div>• 维护困难，用户体验差</div>
                    <div>• 移动端支持不足</div>
                  </div>
                </div>
                <div className="p-4 bg-red-800/20 rounded-xl border border-red-500/20">
                  <div className="text-red-300 font-semibold mb-2">🏗️ ABAP业务逻辑</div>
                  <div className="text-red-200 text-sm space-y-1">
                    <div>• 单体架构，扩展性差</div>
                    <div>• 人才稀缺，技术债务高</div>
                    <div>• 业务耦合度高</div>
                  </div>
                </div>
                <div className="p-4 bg-red-800/20 rounded-xl border border-red-500/20">
                  <div className="text-red-300 font-semibold mb-2">💾 HANA数据库</div>
                  <div className="text-red-200 text-sm space-y-1">
                    <div>• 集中存储，耦合紧密</div>
                    <div>• 单点风险，扩展受限</div>
                    <div>• 成本高昂</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Phase 1 */}
          <div className="relative">
            <div className="absolute left-0 top-0 w-2 h-full bg-gradient-to-b from-green-500 to-green-700 rounded-full"></div>
            <div className="ml-8 p-6 bg-gradient-to-r from-green-900/30 to-emerald-800/30 rounded-2xl border border-green-500/30">
              <h3 className="text-xl font-bold text-green-300 mb-4 flex items-center gap-2">
                <span className="px-3 py-1 bg-green-500 text-white rounded-full text-sm">一期</span>
                合同管理微服务化 (3-4个月)
                <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">效率提升200%</span>
              </h3>
              
              <div className="grid grid-cols-2 gap-6 mb-4">
                <div>
                  <h4 className="text-green-200 font-semibold mb-3 flex items-center gap-2">
                    <span className="text-lg">🎯</span>
                    核心能力建设
                  </h4>
                  <div className="space-y-3">
                    <div className="p-3 bg-green-800/20 rounded-lg border border-green-500/20">
                      <div className="text-green-300 font-medium">🤖 合同智能管理</div>
                      <div className="text-green-200 text-sm">AI辅助审核 • 风险预警 • 版本控制</div>
                    </div>
                    <div className="p-3 bg-green-800/20 rounded-lg border border-green-500/20">
                      <div className="text-green-300 font-medium">🌐 现代化界面</div>
                      <div className="text-green-200 text-sm">Amis低代码 • 响应式设计 • 移动端支持</div>
                    </div>
                    <div className="p-3 bg-green-800/20 rounded-lg border border-green-500/20">
                      <div className="text-green-300 font-medium">🔄 数据同步引擎</div>
                      <div className="text-green-200 text-sm">实时CDC • 双写机制 • 一致性保障</div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-green-200 font-semibold mb-3 flex items-center gap-2">
                    <span className="text-lg">⚙️</span>
                    技术架构
                  </h4>
                  <div className="space-y-3">
                    <div className="p-3 bg-green-800/20 rounded-lg border border-green-500/20">
                      <div className="text-green-300 font-medium">🔧 Spring Boot微服务</div>
                      <div className="text-green-200 text-sm">contract-service • clause-service • agreement-service</div>
                    </div>
                    <div className="p-3 bg-green-800/20 rounded-lg border border-green-500/20">
                      <div className="text-green-300 font-medium">💾 双数据库架构</div>
                      <div className="text-green-200 text-sm">SAP HANA (主) • MySQL (备) • 实时同步</div>
                    </div>
                    <div className="p-3 bg-green-800/20 rounded-lg border border-green-500/20">
                      <div className="text-green-300 font-medium">🎨 现代化前端</div>
                      <div className="text-green-200 text-sm">Amis低代码平台 • PWA支持 • 组件化设计</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-800/10 rounded-lg border border-green-500/20">
                <div className="text-green-300 font-medium">关键指标</div>
                <div className="flex gap-4 text-sm">
                  <span className="text-green-200">响应时间: <span className="text-green-400 font-semibold">&lt;0.5s</span></span>
                  <span className="text-green-200">TPS: <span className="text-green-400 font-semibold">&gt;10,000</span></span>
                  <span className="text-green-200">同步延迟: <span className="text-green-400 font-semibold">&lt;10s</span></span>
                </div>
              </div>
            </div>
          </div>

          {/* Phase 2 */}
          <div className="relative">
            <div className="absolute left-0 top-0 w-2 h-full bg-gradient-to-b from-blue-500 to-blue-700 rounded-full"></div>
            <div className="ml-8 p-6 bg-gradient-to-r from-blue-900/30 to-indigo-800/30 rounded-2xl border border-blue-500/30">
              <h3 className="text-xl font-bold text-blue-300 mb-4 flex items-center gap-2">
                <span className="px-3 py-1 bg-blue-500 text-white rounded-full text-sm">二期</span>
                返利计算引擎重构 (4-6个月)
                <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs">性能提升1000%</span>
              </h3>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="p-4 bg-blue-800/20 rounded-xl border border-blue-500/20">
                  <div className="text-blue-300 font-semibold mb-2">🌊 分布式计算引擎</div>
                  <div className="text-blue-200 text-sm space-y-1">
                    <div>• 流式计算，弹性扩容</div>
                    <div>• 高并发处理</div>
                    <div>• Flink计算引擎</div>
                  </div>
                </div>
                <div className="p-4 bg-blue-800/20 rounded-xl border border-blue-500/20">
                  <div className="text-blue-300 font-semibold mb-2">🧮 算法配置平台</div>
                  <div className="text-blue-200 text-sm space-y-1">
                    <div>• 可视化配置</div>
                    <div>• 规则引擎，A/B测试</div>
                    <div>• 热更新机制</div>
                  </div>
                </div>
                <div className="p-4 bg-blue-800/20 rounded-xl border border-blue-500/20">
                  <div className="text-blue-300 font-semibold mb-2">🤖 AI辅助决策</div>
                  <div className="text-blue-200 text-sm space-y-1">
                    <div>• 智能推荐</div>
                    <div>• 异常检测</div>
                    <div>• 预测分析</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Phase 3 */}
          <div className="relative">
            <div className="absolute left-0 top-0 w-2 h-full bg-gradient-to-b from-orange-500 to-orange-700 rounded-full"></div>
            <div className="ml-8 p-6 bg-gradient-to-r from-orange-900/30 to-amber-800/30 rounded-2xl border border-orange-500/30">
              <h3 className="text-xl font-bold text-orange-300 mb-4 flex items-center gap-2">
                <span className="px-3 py-1 bg-orange-500 text-white rounded-full text-sm">三期</span>
                结算报表平台化 (3-4个月)
                <span className="px-2 py-1 bg-orange-600 text-white rounded text-xs">决策实时化</span>
              </h3>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="p-4 bg-orange-800/20 rounded-xl border border-orange-500/20">
                  <div className="text-orange-300 font-semibold mb-2">🏗️ 实时数据湖</div>
                  <div className="text-orange-200 text-sm space-y-1">
                    <div>• 多源数据整合</div>
                    <div>• 实时ETL，数据血缘</div>
                    <div>• Spark计算引擎</div>
                  </div>
                </div>
                <div className="p-4 bg-orange-800/20 rounded-xl border border-orange-500/20">
                  <div className="text-orange-300 font-semibold mb-2">📊 自助式报表</div>
                  <div className="text-orange-200 text-sm space-y-1">
                    <div>• 拖拽式设计</div>
                    <div>• 实时查询</div>
                    <div>• 多维分析</div>
                  </div>
                </div>
                <div className="p-4 bg-orange-800/20 rounded-xl border border-orange-500/20">
                  <div className="text-orange-300 font-semibold mb-2">🎯 智能运营</div>
                  <div className="text-orange-200 text-sm space-y-1">
                    <div>• 自动化结算</div>
                    <div>• 异常监控</div>
                    <div>• 智能告警</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Phase 4 */}
          <div className="relative">
            <div className="absolute left-0 top-0 w-2 h-full bg-gradient-to-b from-purple-500 to-purple-700 rounded-full"></div>
            <div className="ml-8 p-6 bg-gradient-to-r from-purple-900/30 to-violet-800/30 rounded-2xl border border-purple-500/30">
              <h3 className="text-xl font-bold text-purple-300 mb-4 flex items-center gap-2">
                <span className="px-3 py-1 bg-purple-500 text-white rounded-full text-sm">四期</span>
                全面云原生化 (2-3个月)
                <span className="px-2 py-1 bg-purple-600 text-white rounded text-xs">运维自动化</span>
              </h3>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="p-4 bg-purple-800/20 rounded-xl border border-purple-500/20">
                  <div className="text-purple-300 font-semibold mb-2">☁️ 多云架构</div>
                  <div className="text-purple-200 text-sm space-y-1">
                    <div>• 混合云部署</div>
                    <div>• 容灾备份</div>
                    <div>• 弹性伸缩</div>
                  </div>
                </div>
                <div className="p-4 bg-purple-800/20 rounded-xl border border-purple-500/20">
                  <div className="text-purple-300 font-semibold mb-2">🤖 AIOps运维</div>
                  <div className="text-purple-200 text-sm space-y-1">
                    <div>• 智能监控</div>
                    <div>• 自动修复</div>
                    <div>• 预测性维护</div>
                  </div>
                </div>
                <div className="p-4 bg-purple-800/20 rounded-xl border border-purple-500/20">
                  <div className="text-purple-300 font-semibold mb-2">🔍 全链路智能</div>
                  <div className="text-purple-200 text-sm space-y-1">
                    <div>• 端到端追踪</div>
                    <div>• 性能优化</div>
                    <div>• 用户体验监控</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ROI Analysis */}
        <div className="mt-8 p-6 bg-gradient-to-r from-emerald-900/50 to-teal-900/50 rounded-2xl border border-emerald-500/30">
          <h3 className="text-2xl font-bold text-emerald-300 mb-4 flex items-center gap-2">
            <span className="text-3xl">💰</span>
            投资回报分析
          </h3>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center p-4 bg-emerald-800/20 rounded-xl">
              <div className="text-3xl font-bold text-emerald-400">1100万</div>
              <div className="text-emerald-300">总投资</div>
              <div className="text-emerald-200 text-sm">分期投入，风险可控</div>
            </div>
            <div className="text-center p-4 bg-emerald-800/20 rounded-xl">
              <div className="text-3xl font-bold text-emerald-400">1800万</div>
              <div className="text-emerald-300">3年收益</div>
              <div className="text-emerald-200 text-sm">效率提升+成本节约</div>
            </div>
            <div className="text-center p-4 bg-emerald-800/20 rounded-xl">
              <div className="text-3xl font-bold text-emerald-400">164%</div>
              <div className="text-emerald-300">ROI</div>
              <div className="text-emerald-200 text-sm">投资回报率</div>
            </div>
            <div className="text-center p-4 bg-emerald-800/20 rounded-xl">
              <div className="text-3xl font-bold text-emerald-400">12-15月</div>
              <div className="text-emerald-300">改造周期</div>
              <div className="text-emerald-200 text-sm">分期实施</div>
            </div>
          </div>
        </div>

        {/* Success Metrics */}
        <div className="mt-8 grid grid-cols-2 gap-6">
          <div className="p-6 bg-gradient-to-r from-cyan-900/50 to-blue-900/50 rounded-2xl border border-cyan-500/30">
            <h3 className="text-xl font-bold text-cyan-300 mb-4 flex items-center gap-2">
              <span className="text-2xl">📈</span>
              关键成功指标
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-cyan-800/20 rounded-lg">
                <span className="text-cyan-200">系统响应时间</span>
                <span className="text-cyan-400 font-bold">&lt; 500ms</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-cyan-800/20 rounded-lg">
                <span className="text-cyan-200">并发处理能力</span>
                <span className="text-cyan-400 font-bold">&gt; 50,000 TPS</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-cyan-800/20 rounded-lg">
                <span className="text-cyan-200">系统可用性</span>
                <span className="text-cyan-400 font-bold">&gt; 99.99%</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-cyan-800/20 rounded-lg">
                <span className="text-cyan-200">开发效率提升</span>
                <span className="text-cyan-400 font-bold">500%</span>
              </div>
            </div>
          </div>

          <div className="p-6 bg-gradient-to-r from-rose-900/50 to-pink-900/50 rounded-2xl border border-rose-500/30">
            <h3 className="text-xl font-bold text-rose-300 mb-4 flex items-center gap-2">
              <span className="text-2xl">🛡️</span>
              风险控制措施
            </h3>
            <div className="space-y-3">
              <div className="p-3 bg-rose-800/20 rounded-lg border border-rose-500/20">
                <div className="text-rose-300 font-medium">🔴 高风险</div>
                <div className="text-rose-200 text-sm">数据一致性 → 双写+校验+补偿</div>
              </div>
              <div className="p-3 bg-yellow-800/20 rounded-lg border border-yellow-500/20">
                <div className="text-yellow-300 font-medium">🟡 中风险</div>
                <div className="text-yellow-200 text-sm">技术债务 → 代码重构+技能培训</div>
              </div>
              <div className="p-3 bg-green-800/20 rounded-lg border border-green-500/20">
                <div className="text-green-300 font-medium">🟢 低风险</div>
                <div className="text-green-200 text-sm">用户接受度 → 培训+操作指南</div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-slate-400 text-sm mt-8 p-4 border-t border-slate-700">
          内部资料，严格保密 | 返利引擎重构战略规划 | 企业数字化转型核心项目
        </div>
      </div>
    </div>
  )
}
