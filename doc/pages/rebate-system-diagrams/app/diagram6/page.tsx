'use client'

import React from 'react'

export default function Diagram6() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-4">
      <div className="max-w-[1800px] mx-auto h-screen flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-gradient-to-r from-emerald-400 to-teal-500 rounded"></div>
            <h1 className="text-3xl font-bold text-white">返利引擎一期改造前后对比</h1>
            <div className="px-3 py-1 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full text-white text-sm font-semibold">
              Before & After
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold">商济健康</div>
              <div className="text-red-300 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Comparison SVG */}
        <div className="flex-1 bg-gradient-to-br from-slate-800/50 to-gray-800/50 rounded-2xl p-6 border border-slate-600/50 relative overflow-hidden">
          {/* Comparison SVG */}
          <svg viewBox="0 0 1600 800" className="w-full h-full">
            <defs>
              {/* Gradients */}
              <linearGradient id="beforeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#991b1b" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="afterGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#059669" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#047857" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#059669" stopOpacity="0.8"/>
              </linearGradient>

              {/* Arrow marker */}
              <marker id="transformArrow" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
                <polygon points="0 0, 15 5, 0 10" fill="url(#arrowGrad)"/>
              </marker>
            </defs>

            {/* Background grid */}
            <pattern id="compGrid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#374151" strokeWidth="1" opacity="0.3"/>
            </pattern>
            <rect width="1600" height="800" fill="url(#compGrid)"/>

            {/* Title */}
            <text x="800" y="40" textAnchor="middle" fontSize="24" fontWeight="bold" fill="#f8fafc">
              返利引擎分阶段重构架构图 (2025-2026)
            </text>

            {/* Phase 1 - Current SAP + Data Sources */}
            <g id="phase1Arch">
              <rect x="50" y="80" width="650" height="650" rx="15" fill="url(#beforeGrad)" stroke="#dc2626" strokeWidth="3"/>
              <text x="375" y="110" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">第一阶段 (2025 Q4) - 核心数据源识别</text>

              {/* Core Data Sources */}
              <rect x="80" y="130" width="590" height="100" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="375" y="155" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">核心数据源 - 返利计算底层数据</text>
              <text x="90" y="175" fontSize="11" fill="white">🔵 BDP推送预处理销售单 • 实时销售数据 • 门店销售明细 • 商品销售统计</text>
              <text x="90" y="195" fontSize="11" fill="white">🔵 SAP采购单据 • 采购订单数据 • 供应商结算 • 采购价格信息</text>
              <text x="90" y="215" fontSize="11" fill="white">🔵 SAP配送单据 • 配送明细数据 • 物流成本 • 配送服务费用</text>

              {/* Phase 1 Deliverables */}
              <rect x="80" y="250" width="590" height="180" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="375" y="275" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">第一阶段交付内容 (2025 Q4)</text>

              <rect x="100" y="295" width="170" height="120" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="185" y="315" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">合同&条款&协议</text>
              <text x="185" y="335" textAnchor="middle" fontSize="11" fill="white">功能界面设计</text>
              <text x="110" y="355" fontSize="9" fill="white">• Amis低代码界面</text>
              <text x="110" y="370" fontSize="9" fill="white">• 计算类返利模板导入</text>
              <text x="110" y="385" fontSize="9" fill="white">• SAP现有功能并行</text>
              <text x="110" y="400" fontSize="9" fill="white">• 逐步替换策略</text>

              <rect x="290" y="295" width="170" height="120" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="375" y="315" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">返利计算&分摊</text>
              <text x="375" y="335" textAnchor="middle" fontSize="11" fill="white">算法逻辑分析</text>
              <text x="300" y="355" fontSize="9" fill="white">• SAP算法逻辑分析</text>
              <text x="300" y="370" fontSize="9" fill="white">• 转换为Java脚本</text>
              <text x="300" y="385" fontSize="9" fill="white">• ZREFM0026/0048解析</text>
              <text x="300" y="400" fontSize="9" fill="white">• 计算引擎设计</text>

              <rect x="480" y="295" width="170" height="120" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="565" y="315" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">计提&月结&年结</text>
              <text x="565" y="335" textAnchor="middle" fontSize="11" fill="white">逻辑梳理设计</text>
              <text x="490" y="355" fontSize="9" fill="white">• 月结&年结逻辑梳理</text>
              <text x="490" y="370" fontSize="9" fill="white">• 技术方式设计</text>
              <text x="490" y="385" fontSize="9" fill="white">• 定时任务调度</text>
              <text x="490" y="400" fontSize="9" fill="white">• 状态管理机制</text>

              {/* Additional Phase 1 Deliverables */}
              <rect x="80" y="450" width="280" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="220" y="475" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">结算&返利免付</text>
              <text x="90" y="495" fontSize="11" fill="white">• 产品设计</text>
              <text x="90" y="515" fontSize="11" fill="white">• 外部接口识别</text>
              <text x="90" y="535" fontSize="11" fill="white">• 接口设计</text>
              <text x="90" y="555" fontSize="11" fill="white">• 业务流程梳理</text>

              <rect x="390" y="450" width="280" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="530" y="475" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">返利报表</text>
              <text x="400" y="495" fontSize="11" fill="white">• 报表业务逻辑梳理</text>
              <text x="400" y="515" fontSize="11" fill="white">• 数据交付底层表设计</text>
              <text x="400" y="535" fontSize="11" fill="white">• 报表模板设计</text>
              <text x="400" y="555" fontSize="11" fill="white">• 数据可视化方案</text>

              {/* Phase 1 Key Data Sources */}
              <rect x="80" y="590" width="590" height="120" rx="8" fill="rgba(220, 38, 38, 0.3)" stroke="#dc2626" strokeWidth="2"/>
              <text x="375" y="615" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">核心数据源集成 - 返利计算基础</text>
              <text x="90" y="635" fontSize="11" fill="white">🔵 BDP销售数据: 实时推送 | 门店销售明细 | 商品销售统计 | 销售金额累计</text>
              <text x="90" y="655" fontSize="11" fill="white">🔵 SAP采购数据: 采购订单 | 供应商结算 | 采购价格 | 采购数量累计</text>
              <text x="90" y="675" fontSize="11" fill="white">🔵 SAP配送数据: 配送明细 | 物流成本 | 配送服务费 | 配送数量统计</text>
              <text x="90" y="695" fontSize="11" fill="white">🔵 数据同步机制: 实时ETL | 数据校验 | 异常处理 | 补偿机制</text>
            </g>

            {/* Transformation Arrow */}
            <g id="transformArrow">
              <path d="M 720 400 L 880 400" stroke="url(#arrowGrad)" strokeWidth="8" fill="none" markerEnd="url(#transformArrow)"/>
              <rect x="750" y="370" width="100" height="60" rx="30" fill="#1f2937" stroke="#374151" strokeWidth="2"/>
              <text x="800" y="390" textAnchor="middle" fontSize="12" fontWeight="bold" fill="#f59e0b">渐进式重构</text>
              <text x="800" y="410" textAnchor="middle" fontSize="10" fill="#fbbf24">4阶段实施</text>
            </g>

            {/* Phase 2-4 - Complete Rebate Engine */}
            <g id="finalArch">
              <rect x="900" y="80" width="650" height="650" rx="15" fill="url(#afterGrad)" stroke="#059669" strokeWidth="3"/>
              <text x="1225" y="110" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">第二-四阶段 (2025Q4-2026Q2) - 完整返利引擎</text>

              {/* Data Integration Layer */}
              <rect x="930" y="130" width="590" height="100" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1225" y="155" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">数据集成层 - 核心数据源统一处理</text>
              <text x="940" y="175" fontSize="11" fill="white">🟢 BDP销售数据实时接入 • Kafka消息队列 • 实时ETL处理 • 数据清洗标准化</text>
              <text x="940" y="195" fontSize="11" fill="white">🟢 SAP采购/配送数据同步 • RFC接口调用 • 增量数据抽取 • 数据一致性校验</text>
              <text x="940" y="215" fontSize="11" fill="white">🟢 统一数据模型 • 主数据管理 • 数据血缘追踪 • 质量监控告警</text>

              {/* Rebate Calculation Engine */}
              <rect x="930" y="270" width="590" height="160" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1225" y="295" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">返利计算引擎 - 核心算法转换</text>

              <rect x="950" y="315" width="170" height="100" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1035" y="335" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZREFM0026</text>
              <text x="1035" y="355" textAnchor="middle" fontSize="11" fill="white">普通返利计算</text>
              <text x="960" y="375" fontSize="9" fill="white">• 金额返利(A类型)</text>
              <text x="960" y="390" fontSize="9" fill="white">• 数量返利(T类型)</text>
              <text x="960" y="405" fontSize="9" fill="white">• 比例返利(V类型)</text>

              <rect x="1140" y="315" width="170" height="100" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1225" y="335" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZREFM0048</text>
              <text x="1225" y="355" textAnchor="middle" fontSize="11" fill="white">促销返利计算</text>
              <text x="1150" y="375" fontSize="9" fill="white">• 阶梯返利(P类型)</text>
              <text x="1150" y="390" fontSize="9" fill="white">• 促销活动返利</text>
              <text x="1150" y="405" fontSize="9" fill="white">• 动态规则引擎</text>

              <rect x="1330" y="315" width="170" height="100" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1415" y="335" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">计算数据源</text>
              <text x="1415" y="355" textAnchor="middle" fontSize="11" fill="white">ZRET0017累计表</text>
              <text x="1340" y="375" fontSize="9" fill="white">• 销售累计数据</text>
              <text x="1340" y="390" fontSize="9" fill="white">• 采购累计数据</text>
              <text x="1340" y="405" fontSize="9" fill="white">• 配送累计数据</text>

              {/* Phase Implementation */}
              <rect x="930" y="450" width="280" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1070" y="475" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">第二阶段 (2025Q4)</text>
              <text x="940" y="495" fontSize="11" fill="white">• 与SAP并行计算</text>
              <text x="940" y="515" fontSize="11" fill="white">• 计算结果对比验证</text>
              <text x="940" y="535" fontSize="11" fill="white">• 产品设计&交付</text>
              <text x="940" y="555" fontSize="11" fill="white">• 接口交付完成</text>

              <rect x="1240" y="450" width="280" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1380" y="475" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">第三-四阶段 (2026)</text>
              <text x="1250" y="495" fontSize="11" fill="white">• 完整产品交付</text>
              <text x="1250" y="515" fontSize="11" fill="white">• 数据共享交付</text>
              <text x="1250" y="535" fontSize="11" fill="white">• 持续改善优化</text>
              <text x="1250" y="555" fontSize="11" fill="white">• 新需求快速迭代</text>

              {/* Final Architecture Benefits */}
              <rect x="930" y="590" width="590" height="120" rx="8" fill="rgba(5, 150, 105, 0.3)" stroke="#059669" strokeWidth="2"/>
              <text x="1225" y="615" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">最终架构收益 - 完整返利引擎能力</text>
              <text x="940" y="635" fontSize="11" fill="white">🟢 数据源统一: BDP+SAP集成 | 实时计算: 秒级响应 | 算法转换: Java实现 | 并行验证: 结果一致</text>
              <text x="940" y="655" fontSize="11" fill="white">🟢 微服务架构: 独立扩展 | Amis前端: 低代码开发 | 云原生部署: K8s容器化 | 监控完善: 全链路追踪</text>
              <text x="940" y="675" fontSize="11" fill="white">🟢 渐进式替换: 零业务中断 | 功能对等: SAP能力保持 | 性能提升: 10倍响应速度 | 成本降低: 50%运维成本</text>
              <text x="940" y="695" fontSize="11" fill="white">🟢 技术现代化: 主流技术栈 | 人才储备: 丰富 | 创新能力: 强 | 业务敏捷: 快速迭代</text>
            </g>

            {/* Bottom Summary */}
            <rect x="50" y="750" width="1500" height="40" rx="8" fill="#1f2937" stroke="#374151" strokeWidth="2"/>
            <text x="800" y="775" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#f8fafc">
              核心策略：BDP+SAP数据源集成 | 返利算法Java转换 | 渐进式4阶段实施 | SAP并行验证 | 零业务中断迁移
            </text>
          </svg>
        </div>
      </div>
    </div>
  )
}
