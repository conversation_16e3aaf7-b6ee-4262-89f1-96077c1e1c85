# 返利系统重构技术架构方案

## 1. 项目背景与目标

### 1.1 现状分析
基于对现有SAP返利系统的深度分析，当前系统存在以下特点：
- **SAP ABAP技术栈**：ZRED0040(合同)、ZRED0041(条款)、ZRED0056(协议)等核心模块
- **集中式架构**：所有业务逻辑集中在SAP系统中
- **数据库依赖**：完全依赖SAP HANA数据库
- **界面局限**：传统SAP GUI界面，用户体验有限

### 1.2 改造目标
- **技术现代化**：从SAP ABAP迁移到SpringBoot微服务架构，结合Amis低代码前端和Easy建模MDD
- **用户体验提升**：现代化Web界面替代传统SAP GUI，采用Amis低代码快速构建管理界面
- **系统解耦**：逐步将业务逻辑从SAP中剥离，构建云原生微服务架构
- **数据安全**：建立异构数据库双写机制，确保"开着飞机换引擎"的零停机迁移

### 1.3 改造策略
采用**"开着飞机换引擎"渐进式改造**策略，确保业务连续性：
- **一期**：合同、条款、协议管理模块迁移 + 双写数据同步
- **二期**：返利计算引擎迁移 + Easy建模MDD服务
- **三期**：结算和报表模块迁移 + 完整监控体系
- **四期**：完全替换SAP返利系统 + 云原生优化

## 2. 全局多期整体改造方案

### 2.1 整体架构演进路线图

```mermaid
graph TB
    subgraph "当前架构 (现状)"
        A1[SAP GUI界面]
        A2[SAP ABAP业务逻辑]
        A3[SAP HANA数据库]
        A4[BDP数据平台]
        A5[外部系统集成]
        
        A1 --> A2
        A2 --> A3
        A2 --> A4
        A2 --> A5
    end
    
    subgraph "一期架构 (合同管理)"
        B1[现代化Web界面]
        B2[Spring Boot微服务]
        B3[SAP HANA数据库]
        B4[微服务数据库]
        B5[数据同步服务]
        B6[SAP ABAP剩余模块]
        
        B1 --> B2
        B2 --> B3
        B2 --> B4
        B3 --> B5
        B5 --> B4
        B6 --> B3
    end
    
    subgraph "二期架构 (计算引擎)"
        C1[Web界面扩展]
        C2[返利计算微服务]
        C3[合同管理微服务]
        C4[微服务数据库集群]
        C5[SAP ABAP最小化]
        
        C1 --> C2
        C1 --> C3
        C2 --> C4
        C3 --> C4
        C5 --> C4
    end
    
    subgraph "目标架构 (完全微服务)"
        D1[统一前端平台]
        D2[API网关]
        D3[微服务集群]
        D4[分布式数据库]
        D5[消息队列]
        D6[监控运维平台]
        
        D1 --> D2
        D2 --> D3
        D3 --> D4
        D3 --> D5
        D6 --> D3
    end
    
    A1 -.-> B1
    A2 -.-> B2
    A3 -.-> B3
    B2 -.-> C2
    B2 -.-> C3
    C2 -.-> D3
    C3 -.-> D3
```

### 2.2 分期改造详细规划

#### 2.2.1 一期：合同管理模块 (3-4个周)
**改造范围**：
- 合同创建/修改/查询 (ZRED0040A/B/C)
- 条款管理 (ZRED0041A)
- 协议管理 (ZRED0056)
- 审批流程

**技术目标**：
- Spring Boot微服务架构
- 现代化Web界面
- 双数据库写入
- 数据同步机制

#### 2.2.2 二期：返利计算引擎 (4-6周)
**改造范围**：
- 返利计算逻辑
- 数据分摊处理
- 批量处理功能
- 算法配置管理

**技术目标**：
- 高性能计算引擎
- 分布式计算能力
- 实时计算支持
- 算法可配置化

#### 2.2.3 三期：结算报表模块 (3-4个周)
**改造范围**：
- 结算单管理
- 报表生成
- 数据导出
- 外部系统集成

**技术目标**：
- 高并发报表服务
- 实时数据展示
- 多格式导出
- API标准化

#### 2.2.4 四期：系统整合优化 (2-3个周)
**改造范围**：
- SAP系统下线
- 数据迁移完成
- 性能优化
- 运维体系建设

**技术目标**：
- 完全微服务化
- 云原生架构
- DevOps体系
- 智能运维

### 2.3 技术栈选型

| 层次 | 当前技术 | 目标技术 | 核心优势 |
|------|---------|---------|---------|
| **前端** | SAP GUI | **Amis低代码** + React + TypeScript | 低代码快速开发，类型安全 |
| **网关** | 无 | Spring Cloud Gateway | 统一入口，路由转发，限流熔断 |
| **后端** | SAP ABAP | **Spring Boot 3** + Spring Cloud | 成熟生态，云原生支持 |
| **建模** | SAP配置表 | **Easy建模MDD服务** | 模型驱动开发，动态接口生成 |
| **数据库** | SAP HANA | **MySQL 8.0** + Redis 6.x | 高性能，成本可控 |
| **消息队列** | 无 | **RabbitMQ** | 可靠消息传递，支持多种模式 |
| **注册中心** | 无 | **Nacos** | 服务发现，配置管理 |
| **监控** | SAP监控 | **Prometheus** + Grafana | 全链路监控，丰富的可视化 |
| **日志** | SAP日志 | ELK Stack | 日志分析 |
| **部署** | SAP服务器 | **Docker** + Kubernetes | 弹性扩容，运维自动化 |

### 2.4 核心创新点

#### 2.4.1 双写数据架构
确保SAP HANA和MySQL数据实时同步，支持零停机迁移：
- **主库写入**：SAP HANA作为主数据库
- **备库同步**：MySQL作为目标数据库
- **一致性保障**：实时校验和自动修复机制

#### 2.4.2 Amis低代码前端
快速构建现代化管理界面，开发效率提升80%：
- **配置化开发**：JSON配置生成页面
- **丰富组件库**：表格、表单、图表等组件
- **响应式设计**：适配多种设备

#### 2.4.3 Easy建模MDD服务
提供模型驱动开发能力，支持动态业务需求：
- **动态模型定义**：运行时定义数据模型
- **自动接口生成**：根据模型自动生成CRUD接口
- **业务规则引擎**：可配置的业务验证规则

## 3. 数据流时序图设计

### 3.1 合同创建业务流程时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as Amis前端
    participant G as API网关
    participant CS as 合同服务
    participant MDD as Easy建模服务
    participant MySQL as MySQL数据库
    participant SAP as SAP HANA
    participant Redis as Redis缓存
    participant MQ as RabbitMQ
    participant AS as 审批服务

    Note over U,AS: 合同创建完整流程

    U->>F: 1.打开合同创建页面
    F->>G: 2.GET /api/v1/contracts/form-schema
    G->>MDD: 3.获取动态表单配置
    MDD->>MySQL: 4.查询表单元数据
    MySQL-->>MDD: 5.返回表单配置
    MDD-->>G: 6.返回动态表单Schema
    G-->>F: 7.返回表单配置
    F-->>U: 8.渲染动态表单界面

    U->>F: 9.填写合同信息并提交
    F->>F: 10.前端数据验证
    F->>G: 11.POST /api/v1/contracts
    G->>G: 12.JWT令牌验证
    G->>CS: 13.转发创建请求

    Note over CS: 合同服务处理阶段
    CS->>CS: 14.业务数据验证<br/>- 伙伴代码验证<br/>- 日期逻辑检查<br/>- 重复性检查

    CS->>Redis: 15.生成合同编号<br/>INCR contract:seq:2024
    Redis-->>CS: 16.返回序列号
    CS->>CS: 17.构建合同实体<br/>格式：HT2024000001

    Note over CS,SAP: 双写数据库操作
    CS->>SAP: 18.写入SAP HANA主库<br/>INSERT INTO ZRETA001
    SAP-->>CS: 19.主库写入成功

    CS->>MySQL: 20.写入MySQL备库<br/>INSERT INTO rebate_contract
    MySQL-->>CS: 21.备库写入成功

    CS->>Redis: 22.缓存合同数据<br/>SET contract:{id} EX 3600
    Redis-->>CS: 23.缓存成功

    Note over CS,MQ: 异步事件发布
    CS->>MQ: 24.发布合同创建事件<br/>contract.created
    MQ->>MQ: 25.事件路由到多个消费者

    CS-->>G: 26.返回创建成功响应
    G-->>F: 27.返回合同信息
    F-->>U: 28.显示创建成功消息

    Note over MQ,AS: 异步审批流程
    MQ->>AS: 29.消费合同创建事件
    AS->>AS: 30.初始化审批流程
    AS->>MySQL: 31.创建审批任务
    AS->>U: 32.发送审批通知

    Note over CS: 数据同步验证
    CS->>CS: 33.定时数据一致性检查
    CS->>SAP: 34.查询SAP数据
    CS->>MySQL: 35.查询MySQL数据
    CS->>CS: 36.对比数据一致性

    alt 数据不一致
        CS->>MQ: 37.发送数据修复事件
        CS->>CS: 38.执行数据修复逻辑
    end
```

### 3.2 返利计算引擎业务流程时序图

```mermaid
sequenceDiagram
    participant U as 用户/定时任务
    participant G as API网关
    participant CE as 返利计算引擎
    participant CS as 合同服务
    participant CLS as 条款服务
    participant AGS as 协议服务
    participant MDD as Easy建模服务
    participant MySQL as MySQL数据库
    participant Redis as Redis缓存
    participant MQ as RabbitMQ
    participant SAP as SAP HANA

    Note over U,SAP: 返利计算完整流程

    U->>G: POST /api/v1/calculation/execute<br/>{计算参数}
    G->>CE: 转发计算请求

    Note over CE: 计算准备阶段
    CE->>CE: 1.解析计算参数<br/>2.验证计算权限<br/>3.检查计算状态

    CE->>Redis: 检查计算锁<br/>GET calc:lock:{period}
    Redis-->>CE: 锁状态检查

    alt 计算已在进行中
        CE-->>G: 返回"计算正在进行中"
        G-->>U: HTTP 409 Conflict
    else 可以开始计算
        CE->>Redis: 设置计算锁<br/>SET calc:lock:{period} EX 3600
        Redis-->>CE: 锁设置成功
    end

    Note over CE,AGS: 获取计算基础数据
    CE->>CS: 获取有效合同列表<br/>getActiveContracts(period)
    CS->>MySQL: SELECT * FROM rebate_contract<br/>WHERE status='03' AND period
    MySQL-->>CS: 返回合同列表
    CS-->>CE: 合同数据

    CE->>CLS: 获取条款配置<br/>getClausesByContracts(contractIds)
    CLS->>MySQL: SELECT * FROM rebate_clause<br/>WHERE contract_id IN (...)
    MySQL-->>CLS: 返回条款列表
    CLS-->>CE: 条款数据

    CE->>AGS: 获取协议配置<br/>getAgreementsByClause(clauseIds)
    AGS->>MySQL: SELECT * FROM rebate_agreement<br/>WHERE clause_id IN (...)
    MySQL-->>AGS: 返回协议列表
    AGS-->>CE: 协议数据

    Note over CE,MDD: 动态获取业务数据
    CE->>MDD: 动态查询销售数据<br/>dynamicQuery("sales_data", params)
    MDD->>MySQL: 执行动态SQL<br/>SELECT * FROM sales_data WHERE...
    MySQL-->>MDD: 销售数据
    MDD-->>CE: 格式化销售数据

    Note over CE: 返利计算核心逻辑
    CE->>CE: 初始化计算引擎<br/>loadCalculationRules()

    loop 每个合同
        CE->>CE: 加载合同配置<br/>contract + clauses + agreements

        loop 每个条款
            CE->>CE: 1.匹配商品组数据<br/>2.应用计算规则<br/>3.执行阶梯计算

            alt 固定返利
                CE->>CE: 计算固定金额返利<br/>amount = fixed_amount
            else 比例返利
                CE->>CE: 计算比例返利<br/>amount = sales * rate
            else 阶梯返利
                CE->>CE: 计算阶梯返利<br/>amount = calculateLadder(sales, ladders)
            end

            CE->>CE: 应用业务规则<br/>- 最大限额检查<br/>- 排他性检查<br/>- 时间范围检查
        end

        CE->>CE: 汇总合同返利结果<br/>aggregateContractRebate()
    end

    Note over CE,MySQL: 保存计算结果
    CE->>MySQL: 批量保存返利明细<br/>INSERT INTO rebate_detail
    MySQL-->>CE: 保存成功确认

    CE->>MySQL: 保存计算汇总<br/>INSERT INTO rebate_summary
    MySQL-->>CE: 保存成功确认

    CE->>Redis: 缓存计算结果<br/>SET calc:result:{period}
    Redis-->>CE: 缓存成功

    Note over CE,MQ: 异步处理和通知
    CE->>MQ: 发送计算完成事件<br/>calculation.completed
    MQ->>MQ: 路由到多个消费者

    CE->>Redis: 释放计算锁<br/>DEL calc:lock:{period}
    Redis-->>CE: 锁释放成功

    CE-->>G: 返回计算结果摘要
    G-->>U: HTTP 200 + 计算结果

    Note over MQ,SAP: 数据同步到SAP
    MQ->>CE: 消费同步事件<br/>sync.to.sap
    CE->>SAP: 同步返利结果到SAP<br/>INSERT INTO ZRET_RESULT
    SAP-->>CE: 同步成功确认

    Note over MQ,U: 结果通知
    MQ->>U: WebSocket推送<br/>"返利计算完成"
    MQ->>U: 邮件通知<br/>发送计算报告
```

## 4. 一期合同管理改造技术方案

### 4.1 一期架构设计

```mermaid
graph TB
    subgraph "前端层"
        F1[Amis低代码前端]
        F2[React + TypeScript]
        F3[Axios HTTP客户端]
        F4[动态表单渲染]
    end

    subgraph "网关层"
        G1[Spring Cloud Gateway]
        G2[认证授权模块]
        G3[限流熔断]
        G4[路由转发]
    end

    subgraph "微服务层"
        M1[合同管理服务<br/>contract-service]
        M2[条款管理服务<br/>clause-service]
        M3[协议管理服务<br/>agreement-service]
        M4[Easy建模MDD服务<br/>mdd-service]
        M5[数据同步服务<br/>sync-service]
        M6[审批流程服务<br/>approval-service]
        M7[返利计算引擎<br/>calculation-service]
    end

    subgraph "数据层"
        D1[SAP HANA数据库<br/>主数据库]
        D2[MySQL数据库<br/>微服务数据库]
        D3[Redis缓存<br/>分布式缓存]
        D4[RabbitMQ<br/>消息队列]
    end

    subgraph "外部系统"
        E1[SAP ABAP剩余模块]
        E2[BDP数据平台]
        E3[监控告警系统]
    end

    F1 --> F2
    F1 --> F3
    F1 --> F4
    F3 --> G1
    G1 --> G2
    G1 --> G3
    G1 --> G4

    G2 --> M1
    G2 --> M2
    G2 --> M3
    G2 --> M4
    G2 --> M5
    G2 --> M6
    G2 --> M7

    M1 --> D1
    M1 --> D2
    M1 --> D3
    M2 --> D1
    M2 --> D2
    M3 --> D1
    M3 --> D2
    M4 --> D2
    M5 --> D1
    M5 --> D2
    M5 --> D4
    M6 --> D3
    M7 --> D2
    M7 --> D3

    M1 --> E1
    M2 --> E1
    M3 --> E1
    M5 --> E2
    M7 --> E3
```

### 4.2 数据同步详细时序图

#### 4.2.1 双写数据同步流程

```mermaid
sequenceDiagram
    participant CS as 合同服务
    participant SAP as SAP HANA
    participant MySQL as MySQL
    participant Redis as Redis
    participant MQ as RabbitMQ
    participant DS as 数据同步服务
    participant Monitor as 监控服务

    Note over CS,Monitor: 双写数据同步完整流程

    CS->>CS: 1.开始事务处理
    CS->>SAP: 2.写入SAP HANA主库<br/>BEGIN TRANSACTION
    SAP-->>CS: 3.主库写入成功

    CS->>MySQL: 4.写入MySQL备库<br/>START TRANSACTION
    MySQL-->>CS: 5.备库写入成功

    CS->>CS: 6.提交事务
    CS->>SAP: 7.COMMIT主库事务
    CS->>MySQL: 8.COMMIT备库事务

    CS->>Redis: 9.更新缓存数据<br/>SET contract:{id}
    Redis-->>CS: 10.缓存更新成功

    CS->>MQ: 11.发送同步确认消息<br/>sync.confirmed
    MQ->>DS: 12.消费同步确认事件

    DS->>DS: 13.记录同步日志
    DS->>Monitor: 14.更新同步监控指标

    Note over DS: 异步数据一致性检查
    DS->>SAP: 15.查询SAP数据校验
    DS->>MySQL: 16.查询MySQL数据校验
    DS->>DS: 17.对比数据一致性

    alt 数据不一致
        DS->>DS: 18.标记数据不一致
        DS->>MQ: 19.发送修复消息<br/>data.repair.needed
        DS->>Monitor: 20.发送告警通知

        DS->>SAP: 21.重新读取SAP数据
        DS->>MySQL: 22.修复MySQL数据
        DS->>DS: 23.重新验证一致性
    end

    DS->>Monitor: 24.更新同步成功指标
```

#### 4.2.2 Easy建模MDD动态接口生成流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as Amis前端
    participant MDD as Easy建模服务
    participant MySQL as MySQL
    participant Redis as Redis
    participant CodeGen as 代码生成器

    Note over U,CodeGen: 动态模型定义和接口生成

    U->>F: 1.访问模型设计器
    F->>MDD: 2.GET /api/v1/models/designer
    MDD->>MySQL: 3.查询现有模型定义
    MySQL-->>MDD: 4.返回模型列表
    MDD-->>F: 5.返回设计器配置
    F-->>U: 6.渲染模型设计界面

    U->>F: 7.设计数据模型<br/>- 定义字段<br/>- 设置验证规则<br/>- 配置关联关系
    F->>MDD: 8.POST /api/v1/models<br/>{模型定义JSON}

    MDD->>MDD: 9.验证模型定义<br/>- 字段类型检查<br/>- 关联关系验证<br/>- 命名规范检查

    MDD->>MySQL: 10.保存模型元数据<br/>INSERT INTO model_definition
    MySQL-->>MDD: 11.保存成功

    MDD->>CodeGen: 12.触发代码生成<br/>generateDynamicAPI(modelDef)
    CodeGen->>CodeGen: 13.生成动态API代码<br/>- CRUD接口<br/>- 查询接口<br/>- 验证逻辑

    CodeGen->>Redis: 14.缓存生成的API<br/>SET api:model:{id}
    Redis-->>CodeGen: 15.缓存成功

    CodeGen-->>MDD: 16.代码生成完成
    MDD-->>F: 17.返回模型创建成功
    F-->>U: 18.显示成功消息

    Note over MDD: 动态API调用
    U->>F: 19.使用生成的API
    F->>MDD: 20.POST /api/v1/dynamic/{modelId}/create
    MDD->>Redis: 21.获取API定义<br/>GET api:model:{id}
    Redis-->>MDD: 22.返回API配置

    MDD->>MDD: 23.动态执行业务逻辑<br/>- 数据验证<br/>- 业务规则检查<br/>- 权限验证

    MDD->>MySQL: 24.执行动态SQL<br/>INSERT INTO {dynamic_table}
    MySQL-->>MDD: 25.执行成功
    MDD-->>F: 26.返回操作结果
    F-->>U: 27.显示操作成功
```

### 4.3 核心微服务设计

#### 4.3.1 合同管理服务 (contract-service)
**功能职责**：
- 合同CRUD操作
- 合同状态管理
- 合同审批流程
- 商品组管理

**核心API设计**：
```java
@RestController
@RequestMapping("/api/v1/contracts")
public class ContractController {
    
    @PostMapping
    public ResponseEntity<ContractDTO> createContract(@RequestBody ContractCreateRequest request);
    
    @PutMapping("/{contractId}")
    public ResponseEntity<ContractDTO> updateContract(@PathVariable String contractId, 
                                                     @RequestBody ContractUpdateRequest request);
    
    @GetMapping("/{contractId}")
    public ResponseEntity<ContractDTO> getContract(@PathVariable String contractId);
    
    @GetMapping
    public ResponseEntity<PageResult<ContractDTO>> getContracts(@RequestParam ContractQueryRequest request);
    
    @PostMapping("/{contractId}/submit")
    public ResponseEntity<Void> submitForApproval(@PathVariable String contractId);
}
```

#### 4.3.2 Easy建模MDD服务 (mdd-service)
**功能职责**：
- 动态模型定义和管理
- 自动API接口生成
- 运行时查询构建
- 数据验证和类型检查

**核心实现**：
```java
@RestController
@RequestMapping("/api/v1/mdd")
public class MDDController {

    @PostMapping("/models")
    public ResponseEntity<ModelDTO> createModel(@RequestBody ModelDefinition definition) {
        // 验证模型定义
        validateModelDefinition(definition);

        // 保存模型元数据
        ModelEntity model = mddService.saveModel(definition);

        // 生成动态API
        apiGenerator.generateDynamicAPI(model);

        return ResponseEntity.ok(convertToDTO(model));
    }

    @PostMapping("/models/{modelId}/query")
    public ResponseEntity<Object> dynamicQuery(@PathVariable String modelId,
                                              @RequestBody DynamicQueryRequest request) {
        return ResponseEntity.ok(mddService.dynamicQuery(modelId, request));
    }

    @PostMapping("/models/{modelId}/save")
    public ResponseEntity<Object> dynamicSave(@PathVariable String modelId,
                                             @RequestBody Map<String, Object> data) {
        return ResponseEntity.ok(mddService.dynamicSave(modelId, data));
    }
}
```

#### 4.3.3 数据同步服务 (sync-service)
**功能职责**：
- SAP HANA到MySQL的数据同步
- 数据一致性校验
- 同步状态监控
- 异常处理和重试

**同步策略**：
```java
@Service
public class DataSyncService {
    
    @Async
    public void syncContractData(String contractId, SyncOperation operation) {
        try {
            // 1. 从SAP HANA读取数据
            ContractEntity sapData = sapHanaRepository.findById(contractId);
            
            // 2. 转换数据格式
            ContractEntity mysqlData = dataConverter.convert(sapData);
            
            // 3. 写入MySQL数据库
            mysqlRepository.save(mysqlData);
            
            // 4. 记录同步日志
            syncLogService.recordSuccess(contractId, operation);
            
        } catch (Exception e) {
            // 异常处理和重试机制
            syncLogService.recordFailure(contractId, operation, e);
            retryService.scheduleRetry(contractId, operation);
        }
    }
}
```

### 3.3 数据库设计方案

#### 3.3.1 异构数据库映射关系

| SAP HANA表 | MySQL表 | 映射说明 |
|-----------|---------|---------|
| ZRET0001 | rebate_contract | 合同主表 |
| ZRET0002 | rebate_clause | 条款主表 |
| ZRET0006 | rebate_agreement | 协议主表 |
| ZRET0003 | rebate_product_group | 商品组表 |
| ZRET0004 | rebate_product_group_item | 商品组明细表 |

#### 3.3.2 MySQL数据库表结构设计

```sql
-- 合同主表
CREATE TABLE rebate_contract (
    id VARCHAR(20) PRIMARY KEY COMMENT '合同编号',
    contract_type VARCHAR(10) NOT NULL COMMENT '合同类型',
    contract_subject VARCHAR(10) NOT NULL COMMENT '合同主体',
    partner_type VARCHAR(1) NOT NULL COMMENT '伙伴类型',
    partner_code VARCHAR(20) NOT NULL COMMENT '伙伴编码',
    purchase_group VARCHAR(10) COMMENT '采购组',
    contract_year VARCHAR(4) NOT NULL COMMENT '签署年度',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    contract_name VARCHAR(100) COMMENT '合同名称',
    settlement_cycle VARCHAR(10) COMMENT '结算周期',
    payment_party VARCHAR(20) COMMENT '支付方',
    payment_cycle VARCHAR(10) COMMENT '付款周期',
    status VARCHAR(2) DEFAULT '01' COMMENT '状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    INDEX idx_partner (partner_type, partner_code),
    INDEX idx_date (start_date, end_date),
    INDEX idx_status (status),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利合同表';

-- 条款主表
CREATE TABLE rebate_clause (
    id VARCHAR(20) PRIMARY KEY COMMENT '条款编号',
    contract_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    clause_desc VARCHAR(200) NOT NULL COMMENT '条款描述',
    rebate_type VARCHAR(10) NOT NULL COMMENT '返利类型',
    calculation_base VARCHAR(10) NOT NULL COMMENT '核算基准',
    rebate_form VARCHAR(10) NOT NULL COMMENT '返利形式',
    quantitative_dimension VARCHAR(10) COMMENT '量化维度',
    rebate_rule VARCHAR(10) COMMENT '返利规则',
    ladder_type VARCHAR(10) COMMENT '阶梯类型',
    price_dimension VARCHAR(10) COMMENT '返利价格维度',
    calculation_method VARCHAR(10) COMMENT '计算方法',
    external_supplier VARCHAR(20) COMMENT '外部供货商',
    external_payer VARCHAR(20) COMMENT '外部支付方',
    product_group_id VARCHAR(20) COMMENT '商品组ID',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    status VARCHAR(2) DEFAULT '01' COMMENT '状态',
    approval_status VARCHAR(2) DEFAULT '01' COMMENT '审批状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    FOREIGN KEY (contract_id) REFERENCES rebate_contract(id),
    INDEX idx_contract (contract_id),
    INDEX idx_type (rebate_type),
    INDEX idx_status (status, approval_status),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利条款表';

-- 协议主表
CREATE TABLE rebate_agreement (
    id VARCHAR(20) PRIMARY KEY COMMENT '协议编号',
    clause_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    organization_level VARCHAR(1) NOT NULL COMMENT '组织级别',
    agreement_subject VARCHAR(10) NOT NULL COMMENT '协议主体',
    payer VARCHAR(20) NOT NULL COMMENT '付款方',
    payer_level VARCHAR(1) NOT NULL COMMENT '付款方级别',
    exchange_method VARCHAR(10) NOT NULL COMMENT '兑换方式',
    exclusive_flag VARCHAR(1) DEFAULT 'N' COMMENT '专属标识',
    payment_type VARCHAR(10) COMMENT '付款类型',
    agreement_amount DECIMAL(15,2) COMMENT '协议金额',
    agreement_type VARCHAR(1) DEFAULT 'F' COMMENT '协议类型',
    status VARCHAR(2) DEFAULT '01' COMMENT '状态',
    approval_status VARCHAR(2) DEFAULT '01' COMMENT '审批状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    FOREIGN KEY (clause_id) REFERENCES rebate_clause(id),
    INDEX idx_clause (clause_id),
    INDEX idx_payer (payer, payer_level),
    INDEX idx_status (status, approval_status),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利协议表';
```

#### 3.3.3 数据同步触发器设计

```sql
-- SAP HANA触发器：监控数据变更并通知微服务
CREATE TRIGGER tr_contract_sync
AFTER INSERT OR UPDATE OR DELETE ON ZRET0001
FOR EACH ROW
BEGIN
    DECLARE sync_operation VARCHAR(10);
    DECLARE contract_data CLOB;

    -- 确定操作类型
    IF INSERTING THEN
        sync_operation := 'INSERT';
        contract_data := TO_CLOB(:NEW);
    ELSIF UPDATING THEN
        sync_operation := 'UPDATE';
        contract_data := TO_CLOB(:NEW);
    ELSE
        sync_operation := 'DELETE';
        contract_data := TO_CLOB(:OLD);
    END IF;

    -- 插入同步队列表
    INSERT INTO ZSYNC_QUEUE (
        table_name,
        operation_type,
        record_id,
        data_content,
        sync_status,
        created_time
    ) VALUES (
        'ZRET0001',
        sync_operation,
        COALESCE(:NEW.ZHT_ID, :OLD.ZHT_ID),
        contract_data,
        'PENDING',
        CURRENT_TIMESTAMP
    );
END;
```

### 3.4 微服务详细设计

#### 3.4.1 合同管理服务架构

```mermaid
graph TB
    subgraph "合同管理服务 (contract-service)"
        C1[Controller层]
        C2[Service层]
        C3[Repository层]
        C4[Entity层]
        C5[DTO层]
        C6[Validation层]
        C7[Exception处理层]

        C1 --> C2
        C2 --> C3
        C3 --> C4
        C1 --> C5
        C1 --> C6
        C1 --> C7
    end

    subgraph "数据访问层"
        D1[SAP HANA Repository]
        D2[MySQL Repository]
        D3[Redis Cache]
    end

    subgraph "外部依赖"
        E1[审批服务]
        E2[用户服务]
        E3[数据同步服务]
    end

    C3 --> D1
    C3 --> D2
    C3 --> D3
    C2 --> E1
    C2 --> E2
    C2 --> E3
```

#### 3.4.2 核心业务逻辑实现

```java
@Service
@Transactional
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractSapRepository sapRepository;

    @Autowired
    private ContractMysqlRepository mysqlRepository;

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private ApprovalService approvalService;

    @Override
    public ContractDTO createContract(ContractCreateRequest request) {
        // 1. 数据验证
        validateContractData(request);

        // 2. 生成合同编号
        String contractId = generateContractId();

        // 3. 构建合同实体
        ContractEntity contract = buildContractEntity(contractId, request);

        // 4. 双写数据库
        try {
            // 主库写入 (SAP HANA)
            ContractEntity sapEntity = sapRepository.save(contract);

            // 备库写入 (MySQL)
            ContractEntity mysqlEntity = convertToMysqlEntity(sapEntity);
            mysqlRepository.save(mysqlEntity);

            // 5. 异步同步确认
            dataSyncService.confirmSync(contractId, "CREATE");

            return convertToDTO(sapEntity);

        } catch (Exception e) {
            // 回滚和异常处理
            handleCreateException(contractId, e);
            throw new BusinessException("合同创建失败", e);
        }
    }

    @Override
    public ContractDTO updateContract(String contractId, ContractUpdateRequest request) {
        // 1. 检查合同存在性和状态
        ContractEntity existingContract = sapRepository.findById(contractId)
            .orElseThrow(() -> new BusinessException("合同不存在"));

        if (!canModifyContract(existingContract)) {
            throw new BusinessException("合同当前状态不允许修改");
        }

        // 2. 数据验证
        validateUpdateData(request, existingContract);

        // 3. 更新合同数据
        updateContractFields(existingContract, request);

        // 4. 双写更新
        try {
            // 主库更新
            ContractEntity updatedSapEntity = sapRepository.save(existingContract);

            // 备库更新
            ContractEntity mysqlEntity = convertToMysqlEntity(updatedSapEntity);
            mysqlRepository.save(mysqlEntity);

            // 5. 异步同步确认
            dataSyncService.confirmSync(contractId, "UPDATE");

            return convertToDTO(updatedSapEntity);

        } catch (Exception e) {
            handleUpdateException(contractId, e);
            throw new BusinessException("合同更新失败", e);
        }
    }

    private void validateContractData(ContractCreateRequest request) {
        // 业务规则验证，对应SAP中的验证逻辑

        // 1. 必填字段验证
        if (StringUtils.isEmpty(request.getPartnerCode())) {
            throw new ValidationException("伙伴编码不能为空");
        }

        // 2. 伙伴代码验证 (对应SAP中的frm_check_partner)
        if (!isValidPartner(request.getPartnerType(), request.getPartnerCode())) {
            throw new ValidationException("伙伴不存在");
        }

        // 3. 日期逻辑验证
        if (request.getStartDate().after(request.getEndDate())) {
            throw new ValidationException("开始日期不能大于结束日期");
        }

        // 4. 合同重复性检查
        if (isDuplicateContract(request)) {
            throw new ValidationException("存在重复的合同");
        }
    }
}
```

### 4.4 Amis低代码前端架构设计

#### 4.4.1 Amis前端技术栈

```mermaid
graph TB
    subgraph "Amis低代码架构"
        A1[Amis渲染引擎]
        A2[JSON Schema配置]
        A3[动态组件渲染]
        A4[事件处理机制]
        A5[数据绑定系统]
    end

    subgraph "基础技术栈"
        F1[React 18]
        F2[TypeScript]
        F3[Axios HTTP客户端]
        F4[Mobx状态管理]
        F5[Webpack构建工具]
    end

    subgraph "业务页面配置"
        P1[合同管理页面配置]
        P2[条款管理页面配置]
        P3[协议管理页面配置]
        P4[审批流程页面配置]
        P5[返利计算页面配置]
    end

    subgraph "Amis组件库"
        C1[CRUD表格组件]
        C2[动态表单组件]
        C3[搜索过滤组件]
        C4[图表展示组件]
        C5[文件上传组件]
        C6[工作流组件]
    end

    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5
    A2 --> F1
    A3 --> F2
    A4 --> F3
    A5 --> F4

    A2 --> P1
    A2 --> P2
    A2 --> P3
    A2 --> P4
    A2 --> P5

    P1 --> C1
    P2 --> C2
    P3 --> C3
    P4 --> C4
    P5 --> C5
```

#### 4.4.2 Amis合同管理页面配置示例

```json
{
  "type": "page",
  "title": "返利合同管理",
  "body": [
    {
      "type": "crud",
      "api": "/api/v1/contracts",
      "syncLocation": false,
      "headerToolbar": [
        {
          "type": "button",
          "label": "新建合同",
          "actionType": "dialog",
          "level": "primary",
          "dialog": {
            "title": "新建合同",
            "size": "lg",
            "body": {
              "type": "form",
              "api": "POST:/api/v1/contracts",
              "body": [
                {
                  "type": "input-text",
                  "name": "contractType",
                  "label": "合同类型",
                  "required": true,
                  "options": [
                    {"label": "采购合同", "value": "01"},
                    {"label": "销售合同", "value": "02"}
                  ]
                },
                {
                  "type": "input-text",
                  "name": "partnerCode",
                  "label": "伙伴编码",
                  "required": true,
                  "validations": {
                    "isLength": {
                      "min": 1,
                      "max": 20
                    }
                  }
                },
                {
                  "type": "input-date-range",
                  "name": "dateRange",
                  "label": "合同期间",
                  "required": true,
                  "format": "YYYY-MM-DD"
                },
                {
                  "type": "textarea",
                  "name": "contractName",
                  "label": "合同名称",
                  "maxLength": 100
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "label": "批量审批",
          "actionType": "ajax",
          "level": "success",
          "api": "POST:/api/v1/contracts/batch-approve",
          "confirmText": "确认批量审批选中的合同？"
        }
      ],
      "filter": {
        "type": "form",
        "wrapWithPanel": false,
        "body": [
          {
            "type": "input-text",
            "name": "contractId",
            "label": "合同编号",
            "placeholder": "请输入合同编号"
          },
          {
            "type": "select",
            "name": "status",
            "label": "合同状态",
            "placeholder": "请选择状态",
            "options": [
              {"label": "草稿", "value": "01"},
              {"label": "待审批", "value": "02"},
              {"label": "已审批", "value": "03"},
              {"label": "已拒绝", "value": "04"}
            ]
          },
          {
            "type": "input-date-range",
            "name": "createTimeRange",
            "label": "创建时间",
            "format": "YYYY-MM-DD"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "合同编号",
          "width": 150,
          "searchable": true
        },
        {
          "name": "contractName",
          "label": "合同名称",
          "width": 200
        },
        {
          "name": "partnerCode",
          "label": "伙伴编码",
          "width": 120
        },
        {
          "name": "contractType",
          "label": "合同类型",
          "width": 100,
          "type": "mapping",
          "map": {
            "01": "采购合同",
            "02": "销售合同"
          }
        },
        {
          "name": "startDate",
          "label": "开始日期",
          "width": 120,
          "type": "date"
        },
        {
          "name": "endDate",
          "label": "结束日期",
          "width": 120,
          "type": "date"
        },
        {
          "name": "status",
          "label": "状态",
          "width": 100,
          "type": "status",
          "map": {
            "01": {"label": "草稿", "level": "info"},
            "02": {"label": "待审批", "level": "warning"},
            "03": {"label": "已审批", "level": "success"},
            "04": {"label": "已拒绝", "level": "danger"}
          }
        },
        {
          "type": "operation",
          "label": "操作",
          "width": 200,
          "buttons": [
            {
              "type": "button",
              "label": "查看",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "合同详情",
                "size": "lg",
                "body": {
                  "type": "service",
                  "api": "/api/v1/contracts/${id}",
                  "body": [
                    {
                      "type": "descriptions",
                      "title": "基本信息",
                      "items": [
                        {"label": "合同编号", "name": "id"},
                        {"label": "合同名称", "name": "contractName"},
                        {"label": "伙伴编码", "name": "partnerCode"},
                        {"label": "合同类型", "name": "contractType"},
                        {"label": "开始日期", "name": "startDate"},
                        {"label": "结束日期", "name": "endDate"}
                      ]
                    }
                  ]
                }
              }
            },
            {
              "type": "button",
              "label": "编辑",
              "level": "primary",
              "actionType": "dialog",
              "visibleOn": "${status == '01'}",
              "dialog": {
                "title": "编辑合同",
                "size": "lg",
                "body": {
                  "type": "form",
                  "api": "PUT:/api/v1/contracts/${id}",
                  "initApi": "/api/v1/contracts/${id}",
                  "body": [
                    {
                      "type": "static",
                      "name": "id",
                      "label": "合同编号"
                    },
                    {
                      "type": "input-text",
                      "name": "contractName",
                      "label": "合同名称",
                      "required": true
                    },
                    {
                      "type": "input-text",
                      "name": "partnerCode",
                      "label": "伙伴编码",
                      "required": true
                    }
                  ]
                }
              }
            },
            {
              "type": "button",
              "label": "提交审批",
              "level": "success",
              "actionType": "ajax",
              "api": "POST:/api/v1/contracts/${id}/submit",
              "confirmText": "确认提交该合同进行审批？",
              "visibleOn": "${status == '01'}"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 3.5.2 核心页面设计

```vue
<!-- 合同管理主页面 -->
<template>
  <div class="contract-management">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="合同编号">
          <el-input v-model="searchForm.contractId" placeholder="请输入合同编号" />
        </el-form-item>
        <el-form-item label="伙伴编码">
          <el-input v-model="searchForm.partnerCode" placeholder="请输入伙伴编码" />
        </el-form-item>
        <el-form-item label="合同状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="草稿" value="01" />
            <el-option label="待审批" value="02" />
            <el-option label="已审批" value="03" />
            <el-option label="已拒绝" value="04" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card">
      <el-button type="primary" @click="handleCreate">新建合同</el-button>
      <el-button type="success" @click="handleBatchApproval">批量审批</el-button>
      <el-button type="info" @click="handleExport">导出</el-button>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="contractList" v-loading="loading">
        <el-table-column prop="id" label="合同编号" width="150" />
        <el-table-column prop="contractName" label="合同名称" width="200" />
        <el-table-column prop="partnerCode" label="伙伴编码" width="120" />
        <el-table-column prop="contractType" label="合同类型" width="100" />
        <el-table-column prop="startDate" label="开始日期" width="120" />
        <el-table-column prop="endDate" label="结束日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)"
                       :disabled="!canEdit(row)">编辑</el-button>
            <el-button size="small" type="success" @click="handleSubmit(row)"
                       :disabled="!canSubmit(row)">提交审批</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { contractApi } from '@/api/contract'
import type { ContractDTO, ContractQueryRequest } from '@/types/contract'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const contractList = ref<ContractDTO[]>([])

const searchForm = reactive<ContractQueryRequest>({
  contractId: '',
  partnerCode: '',
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 方法定义
const handleSearch = async () => {
  loading.value = true
  try {
    const response = await contractApi.getContracts({
      ...searchForm,
      page: pagination.current,
      size: pagination.size
    })
    contractList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  router.push('/contract/create')
}

const handleEdit = (row: ContractDTO) => {
  router.push(`/contract/edit/${row.id}`)
}

const handleSubmit = async (row: ContractDTO) => {
  try {
    await ElMessageBox.confirm('确认提交该合同进行审批？', '提示', {
      type: 'warning'
    })

    await contractApi.submitForApproval(row.id)
    ElMessage.success('提交成功')
    handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败')
    }
  }
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>
```

## 5. 部署架构与运维体系

### 5.1 Kubernetes容器化部署架构

```mermaid
graph TB
    subgraph "外部访问层"
        LB[负载均衡器<br/>Nginx/HAProxy]
        CDN[CDN加速<br/>静态资源]
    end

    subgraph "Kubernetes集群"
        subgraph "Ingress层"
            ING[Ingress Controller<br/>Nginx Ingress]
        end

        subgraph "应用层 Pods"
            GW[API网关<br/>3 replicas]
            CS[合同服务<br/>3 replicas]
            CLS[条款服务<br/>2 replicas]
            AGS[协议服务<br/>2 replicas]
            CE[计算引擎<br/>2 replicas]
            MDD[MDD服务<br/>2 replicas]
            AS[审批服务<br/>2 replicas]
            DS[同步服务<br/>2 replicas]
        end

        subgraph "基础服务层"
            REDIS[Redis集群<br/>3 master + 3 slave]
            MQ[RabbitMQ集群<br/>3 nodes]
            NACOS[Nacos集群<br/>3 nodes]
        end

        subgraph "监控层"
            PROM[Prometheus<br/>2 replicas]
            GRAF[Grafana<br/>2 replicas]
            ALERT[AlertManager<br/>2 replicas]
        end

        subgraph "存储层"
            PVC1[MySQL PVC<br/>SSD存储]
            PVC2[Redis PVC<br/>高速存储]
            PVC3[日志PVC<br/>大容量存储]
        end
    end

    subgraph "外部数据库"
        MYSQL[MySQL主从集群<br/>1主2从]
        SAP[SAP HANA<br/>只读访问]
    end

    subgraph "CI/CD流水线"
        GIT[Git仓库<br/>GitLab]
        JENKINS[Jenkins<br/>构建服务器]
        HARBOR[Harbor<br/>镜像仓库]
        HELM[Helm Charts<br/>部署模板]
    end

    LB --> ING
    CDN --> LB
    ING --> GW

    GW --> CS
    GW --> CLS
    GW --> AGS
    GW --> CE
    GW --> MDD
    GW --> AS

    CS --> REDIS
    CLS --> REDIS
    AGS --> REDIS
    CE --> REDIS

    CS --> MQ
    CLS --> MQ
    DS --> MQ
    AS --> MQ

    CS --> NACOS
    CLS --> NACOS
    AGS --> NACOS

    PROM --> CS
    PROM --> CLS
    PROM --> AGS
    GRAF --> PROM
    ALERT --> PROM

    CS --> MYSQL
    CLS --> MYSQL
    AGS --> MYSQL
    DS --> SAP

    REDIS --> PVC2
    MQ --> PVC3
    MYSQL --> PVC1

    GIT --> JENKINS
    JENKINS --> HARBOR
    HARBOR --> ING
    HELM --> ING
```

### 5.2 监控体系架构

```mermaid
graph TB
    subgraph "应用监控"
        M1[Spring Boot Actuator]
        M2[Micrometer Metrics]
        M3[Custom Business Metrics]
        M4[JVM Metrics]
    end

    subgraph "基础设施监控"
        I1[Kubernetes Metrics Server]
        I2[Node Exporter]
        I3[cAdvisor]
        I4[kube-state-metrics]
    end

    subgraph "数据收集与存储"
        C1[Prometheus Server]
        C2[Prometheus Pushgateway]
        C3[InfluxDB时序数据库]
    end

    subgraph "可视化与告警"
        V1[Grafana Dashboard]
        V2[AlertManager]
        V3[Slack/Email通知]
        V4[PagerDuty集成]
    end

    subgraph "日志系统"
        L1[Filebeat日志收集]
        L2[Logstash数据处理]
        L3[Elasticsearch存储]
        L4[Kibana可视化]
    end

    subgraph "链路追踪"
        T1[Jaeger Tracing]
        T2[OpenTelemetry]
        T3[Zipkin]
    end

    M1 --> C1
    M2 --> C1
    M3 --> C1
    M4 --> C1

    I1 --> C1
    I2 --> C1
    I3 --> C1
    I4 --> C1

    C1 --> V1
    C1 --> V2
    C2 --> C1
    C3 --> V1

    V2 --> V3
    V2 --> V4

    L1 --> L2
    L2 --> L3
    L3 --> L4

    T1 --> V1
    T2 --> T1
    T3 --> T1
```

#### 3.6.2 Docker容器配置

```dockerfile
# 微服务基础镜像
FROM openjdk:17-jdk-slim

# 设置工作目录
WORKDIR /app

# 复制应用JAR文件
COPY target/contract-service-1.0.0.jar app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 3.6.3 Kubernetes部署配置

```yaml
# contract-service部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: contract-service
  namespace: rebate-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: contract-service
  template:
    metadata:
      labels:
        app: contract-service
    spec:
      containers:
      - name: contract-service
        image: rebate/contract-service:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MYSQL_HOST
          valueFrom:
            configMapKeyRef:
              name: database-config
              key: mysql-host
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: mysql-password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
# Service配置
apiVersion: v1
kind: Service
metadata:
  name: contract-service
  namespace: rebate-system
spec:
  selector:
    app: contract-service
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
```

### 3.7 数据迁移和同步策略

#### 3.7.1 数据迁移方案

```mermaid
sequenceDiagram
    participant Admin as 系统管理员
    participant Migration as 迁移工具
    participant SAP as SAP HANA
    participant MySQL as MySQL数据库
    participant Validation as 数据校验

    Admin->>Migration: 启动数据迁移
    Migration->>SAP: 读取历史数据
    SAP-->>Migration: 返回数据集
    Migration->>Migration: 数据转换和清洗
    Migration->>MySQL: 批量写入数据
    MySQL-->>Migration: 写入确认
    Migration->>Validation: 启动数据校验
    Validation->>SAP: 查询源数据
    Validation->>MySQL: 查询目标数据
    Validation->>Validation: 对比数据一致性
    Validation-->>Admin: 校验报告
```

#### 3.7.2 实时同步机制

```java
@Component
public class RealTimeSyncProcessor {

    @Autowired
    private SyncQueueRepository syncQueueRepository;

    @Autowired
    private ContractMysqlRepository contractMysqlRepository;

    @Scheduled(fixedDelay = 5000) // 每5秒执行一次
    public void processSyncQueue() {
        List<SyncQueueEntity> pendingItems = syncQueueRepository
            .findByStatusOrderByCreatedTime("PENDING", PageRequest.of(0, 100));

        for (SyncQueueEntity item : pendingItems) {
            try {
                processSyncItem(item);
                item.setStatus("SUCCESS");
                item.setProcessedTime(LocalDateTime.now());
            } catch (Exception e) {
                item.setStatus("FAILED");
                item.setErrorMessage(e.getMessage());
                item.setRetryCount(item.getRetryCount() + 1);

                // 重试机制
                if (item.getRetryCount() < 3) {
                    item.setStatus("PENDING");
                    item.setNextRetryTime(LocalDateTime.now().plusMinutes(5));
                }
            } finally {
                syncQueueRepository.save(item);
            }
        }
    }

    private void processSyncItem(SyncQueueEntity item) {
        switch (item.getOperationType()) {
            case "INSERT":
                handleInsert(item);
                break;
            case "UPDATE":
                handleUpdate(item);
                break;
            case "DELETE":
                handleDelete(item);
                break;
        }
    }

    private void handleInsert(SyncQueueEntity item) {
        // 解析SAP数据并转换为MySQL格式
        ContractEntity mysqlEntity = parseAndConvert(item.getDataContent());
        contractMysqlRepository.save(mysqlEntity);
    }
}
```

## 6. 实施计划与风险控制

### 6.1 分阶段实施时间线

```mermaid
gantt
    title 返利系统重构实施计划
    dateFormat  YYYY-MM-DD
    section 准备阶段
    环境搭建           :prep1, 2024-01-01, 2024-01-15
    团队培训           :prep2, 2024-01-08, 2024-01-22
    技术选型验证       :prep3, 2024-01-15, 2024-01-29

    section 第一阶段：基础设施
    K8s集群搭建        :infra1, 2024-02-01, 2024-02-15
    CI/CD流水线        :infra2, 2024-02-08, 2024-02-22
    监控体系部署       :infra3, 2024-02-15, 2024-02-29
    数据库环境准备     :infra4, 2024-02-20, 2024-03-05

    section 第二阶段：核心服务开发
    合同管理服务       :core1, 2024-03-01, 2024-03-31
    条款管理服务       :core2, 2024-03-15, 2024-04-15
    协议管理服务       :core3, 2024-04-01, 2024-04-30
    数据同步服务       :core4, 2024-03-20, 2024-04-20

    section 第三阶段：前端开发
    Amis框架搭建       :fe1, 2024-04-15, 2024-04-30
    合同管理界面       :fe2, 2024-05-01, 2024-05-20
    条款管理界面       :fe3, 2024-05-15, 2024-06-05
    协议管理界面       :fe4, 2024-06-01, 2024-06-20

    section 第四阶段：集成测试
    单元测试           :test1, 2024-06-15, 2024-06-30
    集成测试           :test2, 2024-07-01, 2024-07-20
    性能测试           :test3, 2024-07-15, 2024-07-30
    用户验收测试       :test4, 2024-08-01, 2024-08-15

    section 第五阶段：生产部署
    预生产环境部署     :deploy1, 2024-08-15, 2024-08-25
    数据迁移           :deploy2, 2024-08-20, 2024-08-30
    生产环境部署       :deploy3, 2024-09-01, 2024-09-10
    切换上线           :deploy4, 2024-09-10, 2024-09-15

    section 第六阶段：优化完善
    性能优化           :opt1, 2024-09-15, 2024-09-30
    功能完善           :opt2, 2024-10-01, 2024-10-15
    文档整理           :opt3, 2024-10-10, 2024-10-20
    项目总结           :opt4, 2024-10-20, 2024-10-31
```

### 6.2 关键风险评估与应对措施

| 风险类别 | 风险项 | 概率 | 影响 | 风险等级 | 应对策略 |
|---------|-------|------|------|---------|---------|
| **技术风险** | 数据同步延迟/失败 | 高 | 高 | 🔴 极高 | 双写+校验+补偿机制 |
| **技术风险** | 数据一致性问题 | 中 | 高 | 🟡 高 | 实时校验+自动修复 |
| **技术风险** | 性能瓶颈 | 中 | 中 | 🟡 中 | 压力测试+性能调优 |
| **技术风险** | 微服务复杂性 | 高 | 中 | 🟡 高 | 完善监控+链路追踪 |
| **业务风险** | 业务中断 | 低 | 极高 | 🟡 高 | 灰度发布+快速回滚 |
| **业务风险** | 功能缺失 | 中 | 高 | 🟡 高 | 详细需求分析+UAT |
| **业务风险** | 用户接受度低 | 中 | 中 | 🟡 中 | 用户培训+界面优化 |
| **项目风险** | 进度延期 | 中 | 中 | 🟡 中 | 敏捷开发+里程碑管控 |
| **项目风险** | 人员流失 | 低 | 高 | 🟡 中 | 知识文档化+交叉培训 |

### 6.3 成功标准与验收条件

#### 6.3.1 技术指标
- **系统可用性：** ≥ 99.9%
- **响应时间：** 95%请求 < 2秒
- **数据一致性：** ≥ 99.99%
- **同步延迟：** < 10秒
- **错误率：** < 0.1%

#### 6.3.2 业务指标
- **功能完整性：** 100%核心功能迁移
- **用户满意度：** ≥ 90%
- **操作效率：** 提升 ≥ 30%
- **培训通过率：** ≥ 95%

#### 6.3.3 项目指标
- **进度达成率：** ≥ 95%
- **预算控制：** 不超预算10%
- **质量指标：** 缺陷密度 < 1个/KLOC
- **文档完整性：** 100%

## 7. 监控运维体系

### 7.1 监控架构

```mermaid
graph TB
    subgraph "应用监控"
        M1[Spring Boot Actuator]
        M2[Micrometer Metrics]
        M3[Custom Metrics]
    end

    subgraph "基础设施监控"
        I1[Kubernetes Metrics]
        I2[Docker Stats]
        I3[Node Exporter]
    end

    subgraph "数据收集"
        C1[Prometheus]
        C2[Grafana]
        C3[AlertManager]
    end

    subgraph "日志系统"
        L1[Logback]
        L2[Filebeat]
        L3[Elasticsearch]
        L4[Kibana]
    end

    M1 --> C1
    M2 --> C1
    M3 --> C1
    I1 --> C1
    I2 --> C1
    I3 --> C1

    C1 --> C2
    C1 --> C3

    L1 --> L2
    L2 --> L3
    L3 --> L4
```

#### 3.8.2 关键监控指标

| 监控类型 | 监控指标 | 告警阈值 | 处理方式 |
|---------|---------|---------|---------|
| **应用性能** | 响应时间 | >2秒 | 自动扩容 |
| **应用性能** | 错误率 | >5% | 立即告警 |
| **应用性能** | QPS | >1000 | 限流保护 |
| **数据同步** | 同步延迟 | >30秒 | 检查同步服务 |
| **数据同步** | 失败率 | >1% | 人工介入 |
| **系统资源** | CPU使用率 | >80% | 自动扩容 |
| **系统资源** | 内存使用率 | >85% | 自动扩容 |
| **数据库** | 连接数 | >80% | 连接池调优 |

## 4. 实施计划和风险控制

### 4.1 一期实施计划 (12周)

#### 第1-2周：环境准备和基础设施搭建
- [ ] Kubernetes集群搭建
- [ ] MySQL数据库部署
- [ ] Redis缓存部署
- [ ] 监控系统部署
- [ ] CI/CD流水线搭建

#### 第3-4周：数据库设计和数据迁移
- [ ] MySQL数据库表结构设计
- [ ] 数据迁移工具开发
- [ ] 历史数据迁移
- [ ] 数据一致性校验

#### 第5-6周：后端微服务开发
- [ ] 合同管理服务开发
- [ ] 条款管理服务开发
- [ ] 协议管理服务开发
- [ ] 数据同步服务开发

#### 第7-8周：前端页面开发
- [ ] 合同管理页面开发
- [ ] 条款管理页面开发
- [ ] 协议管理页面开发
- [ ] 审批流程页面开发

#### 第9-10周：系统集成和测试
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能测试
- [ ] 安全测试

#### 第11-12周：部署上线和验收
- [ ] 生产环境部署
- [ ] 用户培训
- [ ] 系统验收
- [ ] 文档交付

### 4.2 风险控制措施

#### 4.2.1 技术风险
| 风险项 | 风险等级 | 应对措施 |
|-------|---------|---------|
| 数据同步延迟 | 高 | 实时监控+告警机制 |
| 数据一致性问题 | 高 | 双写+校验+补偿机制 |
| 性能瓶颈 | 中 | 压力测试+性能调优 |
| 系统兼容性 | 中 | 充分测试+灰度发布 |

#### 4.2.2 业务风险
| 风险项 | 风险等级 | 应对措施 |
|-------|---------|---------|
| 业务中断 | 高 | 灰度发布+快速回滚 |
| 数据丢失 | 高 | 数据备份+恢复机制 |
| 用户接受度 | 中 | 用户培训+操作指南 |
| 功能缺失 | 中 | 需求确认+功能测试 |

### 4.3 成功标准

#### 4.3.1 技术指标
- 系统响应时间 < 2秒
- 系统可用性 > 99.9%
- 数据同步延迟 < 10秒
- 数据一致性 > 99.99%

#### 4.3.2 业务指标
- 用户操作效率提升 > 50%
- 界面满意度 > 90%
- 功能完整性 = 100%
- 培训通过率 > 95%

## 8. 技术架构方案总结

### 8.1 方案核心亮点

#### 8.1.1 "开着飞机换引擎"零停机迁移
- **双写数据架构**：SAP HANA作为主库，MySQL作为目标库，确保数据实时同步
- **渐进式切换**：分阶段迁移，每个阶段都可独立验证和回滚
- **业务连续性保障**：整个迁移过程中业务不中断，用户无感知

#### 8.1.2 现代化技术栈组合
- **Amis低代码前端**：开发效率提升80%，快速构建现代化管理界面
- **SpringBoot微服务**：云原生架构，支持弹性扩容和独立部署
- **Easy建模MDD**：模型驱动开发，支持动态业务需求变更
- **Kubernetes容器化**：自动化运维，提升系统可靠性

#### 8.1.3 完整的数据流时序图设计
本方案提供了详细的数据流时序图，清晰展示了：
- **合同创建流程**：从前端提交到数据库存储的完整链路
- **返利计算流程**：复杂业务逻辑的微服务协作过程
- **数据同步流程**：双写机制和一致性保障的技术实现
- **MDD动态接口**：模型驱动开发的完整生命周期

### 8.2 技术创新点

#### 8.2.1 双写数据同步机制
```java
// 核心双写逻辑
public <T> T createWithDualWrite(T entity, String entityType) {
    // 1. 写入SAP HANA (主库)
    T sapResult = sapRepository.save(entity);
    // 2. 写入MySQL (目标库)
    T mysqlResult = mysqlRepository.save(convertToMysql(sapResult));
    // 3. 异步校验和补偿
    asyncValidateAndCompensate(sapResult, mysqlResult);
    return sapResult;
}
```

#### 8.2.2 Amis低代码配置
```json
{
  "type": "crud",
  "api": "/api/v1/contracts",
  "columns": [
    {"name": "contractId", "label": "合同编号", "searchable": true},
    {"name": "contractDesc", "label": "合同描述"},
    {"name": "status", "label": "状态", "type": "status"}
  ],
  "headerToolbar": [
    {"type": "button", "label": "新建", "actionType": "dialog"}
  ]
}
```

#### 8.2.3 Easy建模动态接口
```java
@PostMapping("/models/{modelId}/query")
public Object dynamicQuery(@PathVariable String modelId,
                          @RequestBody DynamicQueryRequest request) {
    return mddService.dynamicQuery(modelId, request);
}
```

### 8.3 预期收益

#### 8.3.1 技术收益
- **开发效率提升50%**：微服务架构 + Amis低代码开发
- **运维成本降低30%**：容器化部署 + 自动化运维
- **系统可用性提升至99.9%**：高可用架构设计
- **响应时间优化60%**：缓存策略 + 性能优化

#### 8.3.2 业务收益
- **用户体验显著提升**：现代化界面 + 操作便捷
- **功能扩展能力增强**：Easy建模 + 动态配置
- **数据处理能力提升**：分布式计算 + 实时处理
- **业务创新支撑**：微服务架构 + 快速迭代

#### 8.3.3 成本收益
- **SAP许可证成本节省**：逐步减少SAP依赖
- **硬件成本优化**：云原生架构 + 弹性扩容
- **人力成本降低**：自动化运维 + 标准化开发
- **维护成本减少**：现代化技术栈 + 社区支持

### 8.4 实施保障

#### 8.4.1 技术保障
- **完整的监控体系**：Prometheus + Grafana全链路监控
- **自动化CI/CD**：Jenkins + Harbor + Kubernetes自动化部署
- **数据一致性保障**：实时校验 + 自动修复机制
- **性能优化策略**：Redis缓存 + 数据库优化

#### 8.4.2 风险控制
- **分阶段实施**：降低单次变更风险
- **灰度发布**：支持快速回滚
- **数据备份**：完整的数据备份和恢复机制
- **应急预案**：详细的故障处理流程

### 8.5 结论

本返利系统重构技术架构方案采用现代化的微服务架构，结合Amis低代码前端和Easy建模MDD服务，实现了从传统SAP系统到云原生架构的平滑迁移。

**核心优势：**
1. **零停机迁移**：双写架构确保业务连续性
2. **技术现代化**：全面拥抱云原生技术栈
3. **开发高效**：低代码 + 模型驱动开发
4. **运维自动化**：容器化 + 监控告警体系
5. **可扩展性强**：微服务架构支持业务快速发展

**实施可行性：**
- 技术方案成熟可靠，有大量成功案例
- 分阶段实施策略降低风险
- 完整的监控和应急机制保障系统稳定
- 详细的时序图和技术细节确保可落地执行

该方案不仅解决了现有系统的技术债务问题，还为未来的业务发展提供了强有力的技术支撑，是一个完整、可落地、可执行的技术重构解决方案。
