'use client'

import React from 'react'

export default function Diagram6() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-4">
      <div className="max-w-[1800px] mx-auto h-screen flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-gradient-to-r from-emerald-400 to-teal-500 rounded"></div>
            <h1 className="text-3xl font-bold text-white">返利引擎一期改造前后对比</h1>
            <div className="px-3 py-1 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full text-white text-sm font-semibold">
              Before & After
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold">商济健康</div>
              <div className="text-red-300 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Comparison SVG */}
        <div className="flex-1 bg-gradient-to-br from-slate-800/50 to-gray-800/50 rounded-2xl p-6 border border-slate-600/50 relative overflow-hidden">
          {/* Comparison SVG */}
          <svg viewBox="0 0 1600 800" className="w-full h-full">
            <defs>
              {/* Gradients */}
              <linearGradient id="beforeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#991b1b" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="afterGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#059669" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#047857" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#059669" stopOpacity="0.8"/>
              </linearGradient>

              {/* Arrow marker */}
              <marker id="transformArrow" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
                <polygon points="0 0, 15 5, 0 10" fill="url(#arrowGrad)"/>
              </marker>
            </defs>

            {/* Background grid */}
            <pattern id="compGrid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#374151" strokeWidth="1" opacity="0.3"/>
            </pattern>
            <rect width="1600" height="800" fill="url(#compGrid)"/>

            {/* Title */}
            <text x="800" y="40" textAnchor="middle" fontSize="24" fontWeight="bold" fill="#f8fafc">
              返利引擎分阶段重构架构图 (2025-2026)
            </text>

            {/* Phase 1 - Current SAP + Data Sources */}
            <g id="phase1Arch">
              <rect x="50" y="80" width="650" height="650" rx="15" fill="url(#beforeGrad)" stroke="#dc2626" strokeWidth="3"/>
              <text x="375" y="110" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">第一阶段 (2025 Q4) - 核心数据源识别</text>

              {/* Core Data Sources */}
              <rect x="80" y="130" width="590" height="100" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="375" y="155" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">核心数据源 - 返利计算底层数据</text>
              <text x="90" y="175" fontSize="11" fill="white">🔵 BDP推送预处理销售单 • 实时销售数据 • 门店销售明细 • 商品销售统计</text>
              <text x="90" y="195" fontSize="11" fill="white">🔵 SAP采购单据 • 采购订单数据 • 供应商结算 • 采购价格信息</text>
              <text x="90" y="215" fontSize="11" fill="white">🔵 SAP配送单据 • 配送明细数据 • 物流成本 • 配送服务费用</text>

              {/* ABAP Business Logic */}
              <rect x="80" y="230" width="590" height="200" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="375" y="255" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">ABAP 业务逻辑层</text>

              <rect x="100" y="275" width="170" height="140" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="185" y="295" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZRED0040</text>
              <text x="185" y="315" textAnchor="middle" fontSize="11" fill="white">合同管理</text>
              <text x="110" y="335" fontSize="9" fill="white">• FORM create_contract</text>
              <text x="110" y="350" fontSize="9" fill="white">• CALL FUNCTION 'APPROVAL'</text>
              <text x="110" y="365" fontSize="9" fill="white">• UPDATE zret0001</text>
              <text x="110" y="380" fontSize="9" fill="white">• 单体架构</text>
              <text x="110" y="395" fontSize="9" fill="white">• 紧耦合设计</text>
              <text x="110" y="410" fontSize="9" fill="white">• 扩展困难</text>

              <rect x="290" y="275" width="170" height="140" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="375" y="295" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZRED0041</text>
              <text x="375" y="315" textAnchor="middle" fontSize="11" fill="white">条款管理</text>
              <text x="300" y="335" fontSize="9" fill="white">• FORM config_clause</text>
              <text x="300" y="350" fontSize="9" fill="white">• CALL FUNCTION 'ALGORITHM'</text>
              <text x="300" y="365" fontSize="9" fill="white">• PERFORM validate</text>
              <text x="300" y="380" fontSize="9" fill="white">• 算法固化</text>
              <text x="300" y="395" fontSize="9" fill="white">• 配置复杂</text>
              <text x="300" y="410" fontSize="9" fill="white">• 性能瓶颈</text>

              <rect x="480" y="275" width="170" height="140" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="565" y="295" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">ZRED0056</text>
              <text x="565" y="315" textAnchor="middle" fontSize="11" fill="white">协议管理</text>
              <text x="490" y="335" fontSize="9" fill="white">• FORM generate_agreement</text>
              <text x="490" y="350" fontSize="9" fill="white">• LOOP AT itab</text>
              <text x="490" y="365" fontSize="9" fill="white">• SELECT FROM zret0007</text>
              <text x="490" y="380" fontSize="9" fill="white">• 批处理慢</text>
              <text x="490" y="395" fontSize="9" fill="white">• 内存限制</text>
              <text x="490" y="410" fontSize="9" fill="white">• 并发问题</text>

              {/* SAP HANA Database */}
              <rect x="80" y="450" width="590" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="375" y="475" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">SAP HANA 数据库</text>
              <text x="90" y="495" fontSize="11" fill="white">• ZRET0001 合同主表 • ZRET0002 条款表 • ZRET0006 协议表 • ZRET0007 组织表</text>
              <text x="90" y="515" fontSize="11" fill="white">• 集中式存储 • 单点故障风险 • 扩展成本高 • 备份恢复复杂</text>
              <text x="90" y="535" fontSize="11" fill="white">• 许可证费用高 • 技术栈单一 • 云化困难 • 运维复杂</text>
              <text x="90" y="555" fontSize="11" fill="white">• 数据孤岛 • 集成困难 • 性能瓶颈 • 创新受限</text>

              {/* Performance Issues */}
              <rect x="80" y="590" width="590" height="120" rx="8" fill="rgba(220, 38, 38, 0.3)" stroke="#dc2626" strokeWidth="2"/>
              <text x="375" y="615" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">性能瓶颈与痛点</text>
              <text x="90" y="635" fontSize="11" fill="white">🔴 响应时间: 3-5秒 | 并发用户: 50人 | 合同创建: 30分钟 | 系统可用性: 95%</text>
              <text x="90" y="655" fontSize="11" fill="white">🔴 开发周期: 2周 | 部署频率: 月度 | 故障恢复: 2小时 | 扩展成本: 高</text>
              <text x="90" y="675" fontSize="11" fill="white">🔴 技术债务: 严重 | 维护成本: 高 | 人才稀缺 | 创新能力: 受限</text>
              <text x="90" y="695" fontSize="11" fill="white">🔴 移动支持: 无 | 云原生: 不支持 | API开放: 困难 | 数据分析: 受限</text>
            </g>

            {/* Transformation Arrow */}
            <g id="transformArrow">
              <path d="M 720 400 L 880 400" stroke="url(#arrowGrad)" strokeWidth="8" fill="none" markerEnd="url(#transformArrow)"/>
              <rect x="750" y="370" width="100" height="60" rx="30" fill="#1f2937" stroke="#374151" strokeWidth="2"/>
              <text x="800" y="390" textAnchor="middle" fontSize="12" fontWeight="bold" fill="#f59e0b">一期改造</text>
              <text x="800" y="410" textAnchor="middle" fontSize="10" fill="#fbbf24">14周迁移</text>
            </g>

            {/* After - Rebate Engine Architecture */}
            <g id="afterArch">
              <rect x="900" y="80" width="650" height="650" rx="15" fill="url(#afterGrad)" stroke="#059669" strokeWidth="3"/>
              <text x="1225" y="110" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">改造后 - 返利引擎微服务架构</text>

              {/* Amis Frontend */}
              <rect x="930" y="130" width="590" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1225" y="155" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">Amis 低代码前端</text>
              <text x="940" y="175" fontSize="11" fill="white">• 现代化用户体验 • 移动端原生支持 • 响应时间&lt;500ms • 实时数据更新</text>
              <text x="940" y="195" fontSize="11" fill="white">• 并发1000+用户 • 合同创建5分钟 • 维护成本低 • 快速迭代</text>

              {/* Rebate Engine Services */}
              <rect x="930" y="230" width="590" height="200" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1225" y="255" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">返利引擎微服务层</text>

              <rect x="950" y="275" width="170" height="140" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1035" y="295" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">合同管理引擎</text>
              <text x="1035" y="315" textAnchor="middle" fontSize="11" fill="white">contract-service</text>
              <text x="960" y="335" fontSize="9" fill="white">• ContractController.create()</text>
              <text x="960" y="350" fontSize="9" fill="white">• WorkflowService.approve()</text>
              <text x="960" y="365" fontSize="9" fill="white">• ContractStateService.update()</text>
              <text x="960" y="380" fontSize="9" fill="white">• 微服务架构</text>
              <text x="960" y="395" fontSize="9" fill="white">• 松耦合设计</text>
              <text x="960" y="410" fontSize="9" fill="white">• 独立扩展</text>

              <rect x="1140" y="275" width="170" height="140" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1225" y="295" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">条款配置引擎</text>
              <text x="1225" y="315" textAnchor="middle" fontSize="11" fill="white">clause-service</text>
              <text x="1150" y="335" fontSize="9" fill="white">• ClauseConfigService.config()</text>
              <text x="1150" y="350" fontSize="9" fill="white">• AlgorithmService.setup()</text>
              <text x="1150" y="365" fontSize="9" fill="white">• ValidationEngine.validate()</text>
              <text x="1150" y="380" fontSize="9" fill="white">• 算法可配置</text>
              <text x="1150" y="395" fontSize="9" fill="white">• 规则引擎</text>
              <text x="1150" y="410" fontSize="9" fill="white">• 高性能缓存</text>

              <rect x="1330" y="275" width="170" height="140" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1415" y="295" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">协议生成引擎</text>
              <text x="1415" y="315" textAnchor="middle" fontSize="11" fill="white">agreement-service</text>
              <text x="1340" y="335" fontSize="9" fill="white">• AgreementGenerator.generate()</text>
              <text x="1340" y="350" fontSize="9" fill="white">• BatchProcessService.process()</text>
              <text x="1340" y="365" fontSize="9" fill="white">• OrganizationService.manage()</text>
              <text x="1340" y="380" fontSize="9" fill="white">• 流式处理</text>
              <text x="1340" y="395" fontSize="9" fill="white">• 异步批处理</text>
              <text x="1340" y="410" fontSize="9" fill="white">• 智能调度</text>

              {/* Dual Database */}
              <rect x="930" y="450" width="590" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1225" y="475" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">双数据库异构架构</text>
              <text x="940" y="495" fontSize="11" fill="white">• SAP HANA (主库) + MySQL (异构表) • 实时双写机制 • 数据一致性保障</text>
              <text x="940" y="515" fontSize="11" fill="white">• 触发器同步 • MD5校验 • 补偿事务 • 故障自愈 • 零业务中断</text>
              <text x="940" y="535" fontSize="11" fill="white">• 分布式存储 • 读写分离 • 弹性扩展 • 云原生支持 • 成本优化</text>
              <text x="940" y="555" fontSize="11" fill="white">• 多数据源 • 易集成 • 高可用 • 技术栈现代化</text>

              {/* Performance Improvements */}
              <rect x="930" y="590" width="590" height="120" rx="8" fill="rgba(5, 150, 105, 0.3)" stroke="#059669" strokeWidth="2"/>
              <text x="1225" y="615" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">性能提升与技术突破</text>
              <text x="940" y="635" fontSize="11" fill="white">🟢 响应时间: &lt;500ms | 并发用户: 1000+ | 合同创建: 5分钟 | 系统可用性: 99.9%</text>
              <text x="940" y="655" fontSize="11" fill="white">🟢 开发周期: 3天 | 部署频率: 日度 | 故障恢复: 5分钟 | 扩展成本: 低</text>
              <text x="940" y="675" fontSize="11" fill="white">🟢 技术债务: 清零 | 维护成本: 低 | 人才丰富 | 创新能力: 强</text>
              <text x="940" y="695" fontSize="11" fill="white">🟢 移动支持: 原生 | 云原生: 全支持 | API开放: 完整 | 数据分析: 实时</text>
            </g>

            {/* Bottom Summary */}
            <rect x="50" y="750" width="1500" height="40" rx="8" fill="#1f2937" stroke="#374151" strokeWidth="2"/>
            <text x="800" y="775" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#f8fafc">
              一期改造核心：表结构保持SAP | 管理页面迁移 | 双数据库架构 | 返利引擎能力构建 | 零业务中断
            </text>
          </svg>
        </div>
      </div>
    </div>
  )
}
