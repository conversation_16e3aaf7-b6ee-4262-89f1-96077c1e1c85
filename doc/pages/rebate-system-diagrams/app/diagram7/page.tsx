'use client'

import React from 'react'

export default function Diagram7() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-6">
      <div className="max-w-[1600px] mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-gradient-to-r from-cyan-400 to-blue-500 rounded"></div>
            <h1 className="text-4xl font-bold text-white">返利引擎战略技术演进架构</h1>
            <div className="px-4 py-2 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full text-white text-sm font-semibold">
              Strategic Technical Evolution
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold text-lg">商济健康</div>
              <div className="text-red-300 text-sm">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* SVG Architecture Diagram */}
        <div className="bg-white rounded-2xl p-8 shadow-2xl mb-8">
          <svg viewBox="0 0 1400 900" className="w-full h-auto">
            {/* Background Grid */}
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" strokeWidth="0.5"/>
              </pattern>
              
              {/* Gradients */}
              <linearGradient id="currentGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#ef4444" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#dc2626" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase1Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#10b981" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#059669" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase2Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#1d4ed8" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase3Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#f59e0b" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#d97706" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase4Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#7c3aed" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="middlewareGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#0891b2" stopOpacity="0.9"/>
              </linearGradient>
            </defs>
            
            <rect width="1400" height="900" fill="url(#grid)"/>
            
            {/* Title */}
            <text x="700" y="40" textAnchor="middle" className="text-2xl font-bold" fill="#1f2937">
              返利引擎技术演进架构图
            </text>
            
            {/* Current State */}
            <g id="currentState">
              <rect x="50" y="80" width="280" height="180" rx="10" fill="url(#currentGrad)" stroke="#dc2626" strokeWidth="2"/>
              <text x="190" y="105" textAnchor="middle" className="text-lg font-bold" fill="white">现状：SAP集中式架构</text>
              
              {/* SAP Components */}
              <rect x="70" y="120" width="80" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="110" y="140" textAnchor="middle" className="text-sm" fill="white">SAP GUI</text>
              <text x="110" y="155" textAnchor="middle" className="text-xs" fill="white">传统界面</text>
              
              <rect x="160" y="120" width="80" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="200" y="140" textAnchor="middle" className="text-sm" fill="white">ABAP</text>
              <text x="200" y="155" textAnchor="middle" className="text-xs" fill="white">业务逻辑</text>
              
              <rect x="250" y="120" width="70" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="285" y="140" textAnchor="middle" className="text-sm" fill="white">HANA</text>
              <text x="285" y="155" textAnchor="middle" className="text-xs" fill="white">数据库</text>
              
              {/* Current Capabilities */}
              <text x="70" y="190" className="text-xs" fill="white">存量能力:</text>
              <text x="70" y="205" className="text-xs" fill="white">• 合同管理 ZRED0040</text>
              <text x="70" y="220" className="text-xs" fill="white">• 条款管理 ZRED0041</text>
              <text x="70" y="235" className="text-xs" fill="white">• 协议管理 ZRED0056</text>
            </g>
            
            {/* Message Bus Layer */}
            <g id="messageBus">
              <rect x="50" y="300" width="1300" height="80" rx="10" fill="url(#middlewareGrad)" stroke="#0891b2" strokeWidth="2"/>
              <text x="700" y="325" textAnchor="middle" className="text-lg font-bold" fill="white">消息总线 & 中间件层</text>
              
              {/* Middleware Components */}
              <rect x="80" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="130" y="360" textAnchor="middle" className="text-sm" fill="white">RabbitMQ</text>
              
              <rect x="200" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="250" y="360" textAnchor="middle" className="text-sm" fill="white">Redis Cluster</text>
              
              <rect x="320" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="370" y="360" textAnchor="middle" className="text-sm" fill="white">Kafka</text>
              
              <rect x="440" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="490" y="360" textAnchor="middle" className="text-sm" fill="white">Nacos</text>
              
              <rect x="560" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="610" y="360" textAnchor="middle" className="text-sm" fill="white">Gateway</text>
              
              <rect x="680" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="730" y="360" textAnchor="middle" className="text-sm" fill="white">Sentinel</text>
              
              <rect x="800" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="850" y="360" textAnchor="middle" className="text-sm" fill="white">Skywalking</text>
              
              <rect x="920" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="970" y="360" textAnchor="middle" className="text-sm" fill="white">Prometheus</text>
              
              <rect x="1040" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1090" y="360" textAnchor="middle" className="text-sm" fill="white">ELK Stack</text>
              
              <rect x="1160" y="340" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1210" y="360" textAnchor="middle" className="text-sm" fill="white">K8s</text>
            </g>
            
            {/* Phase 1 */}
            <g id="phase1">
              <rect x="50" y="420" width="320" height="200" rx="10" fill="url(#phase1Grad)" stroke="#059669" strokeWidth="2"/>
              <text x="210" y="445" textAnchor="middle" className="text-lg font-bold" fill="white">一期：合同管理微服务化</text>
              
              {/* Frontend */}
              <rect x="70" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="115" y="475" textAnchor="middle" className="text-sm" fill="white">Amis低代码</text>
              <text x="115" y="490" textAnchor="middle" className="text-xs" fill="white">前端平台</text>
              
              <rect x="170" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="215" y="475" textAnchor="middle" className="text-sm" fill="white">React Native</text>
              <text x="215" y="490" textAnchor="middle" className="text-xs" fill="white">移动端</text>
              
              <rect x="270" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="315" y="475" textAnchor="middle" className="text-sm" fill="white">API Gateway</text>
              <text x="315" y="490" textAnchor="middle" className="text-xs" fill="white">统一入口</text>
              
              {/* Microservices */}
              <rect x="70" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="105" y="525" textAnchor="middle" className="text-xs" fill="white">contract</text>
              <text x="105" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="150" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="185" y="525" textAnchor="middle" className="text-xs" fill="white">clause</text>
              <text x="185" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="230" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="265" y="525" textAnchor="middle" className="text-xs" fill="white">agreement</text>
              <text x="265" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="310" y="510" width="50" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="335" y="525" textAnchor="middle" className="text-xs" fill="white">sync</text>
              <text x="335" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              {/* Data Layer */}
              <rect x="70" y="555" width="90" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="115" y="570" textAnchor="middle" className="text-xs" fill="white">SAP HANA</text>
              <text x="115" y="580" textAnchor="middle" className="text-xs" fill="white">(主库)</text>
              
              <rect x="170" y="555" width="90" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="215" y="570" textAnchor="middle" className="text-xs" fill="white">MySQL</text>
              <text x="215" y="580" textAnchor="middle" className="text-xs" fill="white">(备库)</text>
              
              <rect x="270" y="555" width="90" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="315" y="570" textAnchor="middle" className="text-xs" fill="white">Redis</text>
              <text x="315" y="580" textAnchor="middle" className="text-xs" fill="white">(缓存)</text>
              
              <text x="70" y="605" className="text-xs" fill="white">改造能力: 合同管理数字化</text>
            </g>
            
            {/* Phase 2 */}
            <g id="phase2">
              <rect x="390" y="420" width="320" height="200" rx="10" fill="url(#phase2Grad)" stroke="#1d4ed8" strokeWidth="2"/>
              <text x="550" y="445" textAnchor="middle" className="text-lg font-bold" fill="white">二期：返利计算引擎重构</text>
              
              {/* Compute Engine */}
              <rect x="410" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="455" y="475" textAnchor="middle" className="text-sm" fill="white">Flink</text>
              <text x="455" y="490" textAnchor="middle" className="text-xs" fill="white">流计算引擎</text>
              
              <rect x="510" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="555" y="475" textAnchor="middle" className="text-sm" fill="white">Spark</text>
              <text x="555" y="490" textAnchor="middle" className="text-xs" fill="white">批计算引擎</text>
              
              <rect x="610" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="655" y="475" textAnchor="middle" className="text-sm" fill="white">算法引擎</text>
              <text x="655" y="490" textAnchor="middle" className="text-xs" fill="white">规则配置</text>
              
              {/* Services */}
              <rect x="410" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="445" y="525" textAnchor="middle" className="text-xs" fill="white">calc</text>
              <text x="445" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="490" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="525" y="525" textAnchor="middle" className="text-xs" fill="white">algorithm</text>
              <text x="525" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="570" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="605" y="525" textAnchor="middle" className="text-xs" fill="white">stream</text>
              <text x="605" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="650" y="510" width="50" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="675" y="525" textAnchor="middle" className="text-xs" fill="white">rule</text>
              <text x="675" y="535" textAnchor="middle" className="text-xs" fill="white">engine</text>
              
              {/* Data */}
              <rect x="410" y="555" width="90" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="455" y="570" textAnchor="middle" className="text-xs" fill="white">分库分表</text>
              <text x="455" y="580" textAnchor="middle" className="text-xs" fill="white">MySQL</text>
              
              <rect x="510" y="555" width="90" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="555" y="570" textAnchor="middle" className="text-xs" fill="white">ClickHouse</text>
              <text x="555" y="580" textAnchor="middle" className="text-xs" fill="white">OLAP</text>
              
              <rect x="610" y="555" width="90" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="655" y="570" textAnchor="middle" className="text-xs" fill="white">ElasticSearch</text>
              <text x="655" y="580" textAnchor="middle" className="text-xs" fill="white">搜索</text>
              
              <text x="410" y="605" className="text-xs" fill="white">改造能力: 分布式计算引擎</text>
            </g>
            
            {/* Phase 3 */}
            <g id="phase3">
              <rect x="730" y="420" width="320" height="200" rx="10" fill="url(#phase3Grad)" stroke="#d97706" strokeWidth="2"/>
              <text x="890" y="445" textAnchor="middle" className="text-lg font-bold" fill="white">三期：结算报表平台化</text>
              
              {/* Data Platform */}
              <rect x="750" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="795" y="475" textAnchor="middle" className="text-sm" fill="white">数据湖</text>
              <text x="795" y="490" textAnchor="middle" className="text-xs" fill="white">Delta Lake</text>
              
              <rect x="850" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="895" y="475" textAnchor="middle" className="text-sm" fill="white">实时ETL</text>
              <text x="895" y="490" textAnchor="middle" className="text-xs" fill="white">DataX</text>
              
              <rect x="950" y="460" width="90" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="995" y="475" textAnchor="middle" className="text-sm" fill="white">报表引擎</text>
              <text x="995" y="490" textAnchor="middle" className="text-xs" fill="white">Superset</text>
              
              {/* Services */}
              <rect x="750" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="785" y="525" textAnchor="middle" className="text-xs" fill="white">settlement</text>
              <text x="785" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="830" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="865" y="525" textAnchor="middle" className="text-xs" fill="white">report</text>
              <text x="865" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="910" y="510" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="945" y="525" textAnchor="middle" className="text-xs" fill="white">analytics</text>
              <text x="945" y="535" textAnchor="middle" className="text-xs" fill="white">service</text>
              
              <rect x="990" y="510" width="50" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1015" y="525" textAnchor="middle" className="text-xs" fill="white">bi</text>
              <text x="1015" y="535" textAnchor="middle" className="text-xs" fill="white">engine</text>
              
              {/* External Systems */}
              <rect x="750" y="555" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="785" y="570" textAnchor="middle" className="text-xs" fill="white">金蝶</text>
              <text x="785" y="580" textAnchor="middle" className="text-xs" fill="white">财务</text>
              
              <rect x="830" y="555" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="865" y="570" textAnchor="middle" className="text-xs" fill="white">SRM</text>
              <text x="865" y="580" textAnchor="middle" className="text-xs" fill="white">采购</text>
              
              <rect x="910" y="555" width="70" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="945" y="570" textAnchor="middle" className="text-xs" fill="white">POS</text>
              <text x="945" y="580" textAnchor="middle" className="text-xs" fill="white">销售</text>
              
              <rect x="990" y="555" width="50" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1015" y="570" textAnchor="middle" className="text-xs" fill="white">BDP</text>
              <text x="1015" y="580" textAnchor="middle" className="text-xs" fill="white">数据</text>
              
              <text x="750" y="605" className="text-xs" fill="white">改造能力: 数据平台化</text>
            </g>
            
            {/* Infrastructure Foundation - Bottom Layer */}
            <g id="infrastructure">
              <rect x="50" y="750" width="1300" height="120" rx="10" fill="url(#phase4Grad)" stroke="#7c3aed" strokeWidth="2"/>
              <text x="700" y="775" textAnchor="middle" className="text-lg font-bold" fill="white">基础设施支撑层 - 现有能力</text>

              {/* DevOps & CI/CD */}
              <rect x="80" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="130" y="805" textAnchor="middle" className="text-sm" fill="white">CI/CD</text>
              <text x="130" y="818" textAnchor="middle" className="text-xs" fill="white">Jenkins</text>

              <rect x="200" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="250" y="805" textAnchor="middle" className="text-sm" fill="white">Docker</text>
              <text x="250" y="818" textAnchor="middle" className="text-xs" fill="white">容器化</text>

              <rect x="320" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="370" y="805" textAnchor="middle" className="text-sm" fill="white">K8s</text>
              <text x="370" y="818" textAnchor="middle" className="text-xs" fill="white">编排</text>

              <rect x="440" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="490" y="805" textAnchor="middle" className="text-sm" fill="white">Grafana</text>
              <text x="490" y="818" textAnchor="middle" className="text-xs" fill="white">监控</text>

              <rect x="560" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="610" y="805" textAnchor="middle" className="text-sm" fill="white">腾讯云</text>
              <text x="610" y="818" textAnchor="middle" className="text-xs" fill="white">云平台</text>

              <rect x="680" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="730" y="805" textAnchor="middle" className="text-sm" fill="white">云MQ</text>
              <text x="730" y="818" textAnchor="middle" className="text-xs" fill="white">消息队列</text>

              <rect x="800" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="850" y="805" textAnchor="middle" className="text-sm" fill="white">云Redis</text>
              <text x="850" y="818" textAnchor="middle" className="text-xs" fill="white">缓存</text>

              <rect x="920" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="970" y="805" textAnchor="middle" className="text-sm" fill="white">云MySQL</text>
              <text x="970" y="818" textAnchor="middle" className="text-xs" fill="white">数据库</text>

              <rect x="1040" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1090" y="805" textAnchor="middle" className="text-sm" fill="white">对象存储</text>
              <text x="1090" y="818" textAnchor="middle" className="text-xs" fill="white">COS</text>

              <rect x="1160" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1210" y="805" textAnchor="middle" className="text-sm" fill="white">网络安全</text>
              <text x="1210" y="818" textAnchor="middle" className="text-xs" fill="white">VPC</text>

              <text x="80" y="850" className="text-xs" fill="white">持续建设: 为上层业务系统提供稳定的基础设施支撑</text>
            </g>
            
            {/* Data Flow Arrows */}
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
              </marker>
            </defs>
            
            {/* Current to Phase 1 */}
            <path d="M 190 260 Q 190 290 210 320" stroke="#374151" strokeWidth="2" fill="none" markerEnd="url(#arrowhead)"/>
            
            {/* Phase 1 to Phase 2 */}
            <path d="M 370 520 Q 380 520 390 520" stroke="#374151" strokeWidth="2" fill="none" markerEnd="url(#arrowhead)"/>
            
            {/* Phase 2 to Phase 3 */}
            <path d="M 710 520 Q 720 520 730 520" stroke="#374151" strokeWidth="2" fill="none" markerEnd="url(#arrowhead)"/>
            

            
            {/* Message Bus Connections */}
            <path d="M 210 380 L 210 420" stroke="#0891b2" strokeWidth="2" fill="none"/>
            <path d="M 550 380 L 550 420" stroke="#0891b2" strokeWidth="2" fill="none"/>
            <path d="M 890 380 L 890 420" stroke="#0891b2" strokeWidth="2" fill="none"/>

            {/* Infrastructure Support Connections */}
            <path d="M 210 620 L 210 750" stroke="#7c3aed" strokeWidth="2" fill="none" strokeDasharray="5,5"/>
            <path d="M 550 620 L 550 750" stroke="#7c3aed" strokeWidth="2" fill="none" strokeDasharray="5,5"/>
            <path d="M 890 620 L 890 750" stroke="#7c3aed" strokeWidth="2" fill="none" strokeDasharray="5,5"/>
            

            
            {/* Technical Focus */}
            <g id="techFocus">
              <text x="50" y="720" className="text-sm font-bold" fill="#374151">演进重点:</text>
              <text x="210" y="720" className="text-sm" fill="#059669">短期</text>
              <text x="550" y="720" className="text-sm" fill="#1d4ed8">中期</text>
              <text x="890" y="720" className="text-sm" fill="#d97706">中长期</text>

              <text x="50" y="735" className="text-sm font-bold" fill="#374151">技术重点:</text>
              <text x="210" y="735" className="text-xs" fill="#059669">双写同步</text>
              <text x="550" y="735" className="text-xs" fill="#1d4ed8">分布式计算</text>
              <text x="890" y="735" className="text-xs" fill="#d97706">数据平台</text>
            </g>
          </svg>
        </div>

        {/* Technical Details */}
        <div className="grid grid-cols-2 gap-6">
          <div className="p-6 bg-gradient-to-r from-slate-800 to-gray-800 rounded-2xl border border-slate-600">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <span className="text-2xl">🔧</span>
              核心技术栈演进
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-300">消息中间件:</span>
                <span className="text-cyan-400">RabbitMQ → 腾讯云MQ → Kafka</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">服务治理:</span>
                <span className="text-cyan-400">Nacos → Gateway → K8s</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">计算引擎:</span>
                <span className="text-cyan-400">ABAP → Spring Boot → Flink</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">数据存储:</span>
                <span className="text-cyan-400">HANA → 云MySQL → 数据湖</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">前端技术:</span>
                <span className="text-cyan-400">SAP GUI → Amis → React</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">基础设施:</span>
                <span className="text-cyan-400">物理机 → Docker → 腾讯云</span>
              </div>
            </div>
          </div>
          
          <div className="p-6 bg-gradient-to-r from-slate-800 to-gray-800 rounded-2xl border border-slate-600">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <span className="text-2xl">📊</span>
              系统协同能力
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-300">数据源系统:</span>
                <span className="text-green-400">11个外部系统</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">消息吞吐量:</span>
                <span className="text-green-400">100万条/秒</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">数据同步延迟:</span>
                <span className="text-green-400">&lt; 100ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">系统可用性:</span>
                <span className="text-green-400">99.99%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">并发处理:</span>
                <span className="text-green-400">10万+ TPS</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-slate-400 text-sm mt-8 p-4 border-t border-slate-700">
          技术架构师视角 | 战略技术演进架构图 | 重点关注系统协同与技术稳定性
        </div>
      </div>
    </div>
  )
}
