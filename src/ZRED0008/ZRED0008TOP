*&---------------------------------------------------------------------*
*& 包含               ZRED0008TOP
*&---------------------------------------------------------------------*
TABLES:sscrfields,zreta001,zreta002,zret0006,lfa1.
TYPE-POOLS: zre02,zre03,zre04 .
TYPES:BEGIN OF ty_alv.
    INCLUDE TYPE zres0054.
TYPES END OF ty_alv.

TYPES:BEGIN OF ty_first_id,
        zhtlx  TYPE  zreta001-zhtlx,
        ekgrp  TYPE  zreta001-ekgrp,
        zbukrs TYPE  zreta001-zbukrs,
        zht_id TYPE  zreta001-zht_id,
        ztk_id TYPE  zreta002-ztk_id,
      END OF ty_first_id,
      tt_first_id TYPE TABLE OF ty_first_id.

TYPES:BEGIN OF ty_exe_id,
        ztk_id   TYPE zreta002-ztk_id,
        zlevel   TYPE /bdl/_level,
        zxybstyp TYPE zreta002-zxybstyp,
        ztktype  TYPE zreta002-ztktype,
        zbukrs   TYPE zreta001-zbukrs,
      END OF ty_exe_id,
      tt_exe_id TYPE TABLE OF ty_exe_id.

DATA:"alv输出
  gt_alv TYPE TABLE OF ty_alv,
  gs_alv TYPE ty_alv.

DATA:
  gT_exe_tk     TYPE tt_exe_id,
  gt_ztk_id_nts TYPE tt_exe_id, "非附加条款
  gt_ztk_id_ats TYPE tt_exe_id. "附加条款数据