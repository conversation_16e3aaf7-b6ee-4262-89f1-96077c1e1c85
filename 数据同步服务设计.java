/**
 * 数据同步服务 - 双写管理器
 * 确保SAP HANA和MySQL数据的一致性
 */
@Service
@Slf4j
public class DataSyncService {

    @Autowired
    private SapHanaRepository sapHanaRepository;
    
    @Autowired
    private MysqlRepository mysqlRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Autowired
    private DataSyncLogRepository syncLogRepository;

    /**
     * 双写操作 - 创建数据
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> T createWithDualWrite(T entity, String entityType) {
        String recordKey = extractRecordKey(entity);
        DataSyncLog syncLog = createSyncLog(entityType, "CREATE", recordKey, entity);
        
        try {
            // 1. 写入SAP HANA (主库)
            T sapResult = (T) sapHanaRepository.save(entity);
            syncLog.setSourceData(JsonUtils.toJson(sapResult));
            
            // 2. 写入MySQL (目标库)
            T mysqlEntity = convertToMysqlEntity(sapResult);
            T mysqlResult = (T) mysqlRepository.save(mysqlEntity);
            syncLog.setTargetData(JsonUtils.toJson(mysqlResult));
            
            // 3. 更新缓存
            updateCache(recordKey, mysqlResult);
            
            // 4. 记录同步成功
            syncLog.setSyncStatus("01");
            syncLog.setProcessedTime(LocalDateTime.now());
            syncLogRepository.save(syncLog);
            
            // 5. 发送同步确认消息
            sendSyncConfirmMessage(entityType, recordKey, "CREATE_SUCCESS");
            
            return sapResult;
            
        } catch (Exception e) {
            // 记录同步失败
            syncLog.setSyncStatus("02");
            syncLog.setErrorMessage(e.getMessage());
            syncLog.setProcessedTime(LocalDateTime.now());
            syncLogRepository.save(syncLog);
            
            // 发送重试消息
            scheduleRetry(syncLog);
            
            throw new DataSyncException("双写操作失败", e);
        }
    }

    /**
     * 双写操作 - 更新数据
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> T updateWithDualWrite(T entity, String entityType) {
        String recordKey = extractRecordKey(entity);
        DataSyncLog syncLog = createSyncLog(entityType, "UPDATE", recordKey, entity);
        
        try {
            // 1. 更新SAP HANA
            T sapResult = (T) sapHanaRepository.save(entity);
            syncLog.setSourceData(JsonUtils.toJson(sapResult));
            
            // 2. 更新MySQL
            T mysqlEntity = convertToMysqlEntity(sapResult);
            T mysqlResult = (T) mysqlRepository.save(mysqlEntity);
            syncLog.setTargetData(JsonUtils.toJson(mysqlResult));
            
            // 3. 更新缓存
            updateCache(recordKey, mysqlResult);
            
            // 4. 记录同步成功
            syncLog.setSyncStatus("01");
            syncLog.setProcessedTime(LocalDateTime.now());
            syncLogRepository.save(syncLog);
            
            return sapResult;
            
        } catch (Exception e) {
            syncLog.setSyncStatus("02");
            syncLog.setErrorMessage(e.getMessage());
            syncLog.setProcessedTime(LocalDateTime.now());
            syncLogRepository.save(syncLog);
            
            scheduleRetry(syncLog);
            throw new DataSyncException("双写更新失败", e);
        }
    }

    /**
     * 异步数据同步处理
     */
    @RabbitListener(queues = "data.sync.queue")
    public void handleAsyncSync(DataSyncMessage message) {
        try {
            switch (message.getOperationType()) {
                case "INSERT":
                    handleAsyncInsert(message);
                    break;
                case "UPDATE":
                    handleAsyncUpdate(message);
                    break;
                case "DELETE":
                    handleAsyncDelete(message);
                    break;
            }
        } catch (Exception e) {
            log.error("异步同步处理失败: {}", message, e);
            handleSyncFailure(message, e);
        }
    }

    /**
     * 数据一致性校验
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟执行一次
    public void validateDataConsistency() {
        List<String> inconsistentRecords = new ArrayList<>();
        
        // 获取最近更新的记录进行校验
        List<DataSyncLog> recentLogs = syncLogRepository.findRecentSuccessLogs(
            LocalDateTime.now().minusHours(1));
        
        for (DataSyncLog log : recentLogs) {
            try {
                if (!validateRecordConsistency(log)) {
                    inconsistentRecords.add(log.getRecordKey());
                    // 触发数据修复
                    scheduleDataRepair(log);
                }
            } catch (Exception e) {
                log.error("数据一致性校验失败: {}", log.getRecordKey(), e);
            }
        }
        
        if (!inconsistentRecords.isEmpty()) {
            log.warn("发现数据不一致记录: {}", inconsistentRecords);
            // 发送告警
            sendInconsistencyAlert(inconsistentRecords);
        }
    }

    /**
     * 数据修复机制
     */
    public void repairDataInconsistency(String recordKey, String entityType) {
        try {
            // 1. 从SAP HANA读取最新数据
            Object sapData = sapHanaRepository.findByKey(recordKey, entityType);
            
            if (sapData != null) {
                // 2. 转换并更新MySQL数据
                Object mysqlData = convertToMysqlEntity(sapData);
                mysqlRepository.save(mysqlData);
                
                // 3. 更新缓存
                updateCache(recordKey, mysqlData);
                
                // 4. 记录修复日志
                recordRepairLog(recordKey, entityType, "SUCCESS");
                
            } else {
                // SAP数据不存在，删除MySQL数据
                mysqlRepository.deleteByKey(recordKey, entityType);
                removeFromCache(recordKey);
                recordRepairLog(recordKey, entityType, "DELETED");
            }
            
        } catch (Exception e) {
            log.error("数据修复失败: {}", recordKey, e);
            recordRepairLog(recordKey, entityType, "FAILED");
        }
    }

    /**
     * 缓存管理
     */
    private void updateCache(String recordKey, Object data) {
        try {
            String cacheKey = "rebate:data:" + recordKey;
            redisTemplate.opsForValue().set(cacheKey, data, Duration.ofHours(24));
        } catch (Exception e) {
            log.warn("缓存更新失败: {}", recordKey, e);
        }
    }

    private void removeFromCache(String recordKey) {
        try {
            String cacheKey = "rebate:data:" + recordKey;
            redisTemplate.delete(cacheKey);
        } catch (Exception e) {
            log.warn("缓存删除失败: {}", recordKey, e);
        }
    }

    /**
     * 重试机制
     */
    private void scheduleRetry(DataSyncLog syncLog) {
        if (syncLog.getRetryCount() < 3) {
            // 延迟重试
            DataSyncMessage retryMessage = DataSyncMessage.builder()
                .entityType(syncLog.getTableName())
                .operationType(syncLog.getOperationType())
                .recordKey(syncLog.getRecordKey())
                .data(syncLog.getSourceData())
                .retryCount(syncLog.getRetryCount() + 1)
                .build();
                
            // 延迟发送重试消息
            rabbitTemplate.convertAndSend("data.sync.retry.queue", retryMessage,
                message -> {
                    message.getMessageProperties().setDelay(
                        (syncLog.getRetryCount() + 1) * 60000); // 递增延迟
                    return message;
                });
        } else {
            // 重试次数超限，发送告警
            sendRetryExhaustedAlert(syncLog);
        }
    }

    /**
     * 实体转换
     */
    private <T> T convertToMysqlEntity(T sapEntity) {
        // 实现SAP实体到MySQL实体的转换逻辑
        // 处理字段映射、数据类型转换等
        return EntityConverter.convert(sapEntity);
    }

    /**
     * 提取记录主键
     */
    private String extractRecordKey(Object entity) {
        // 通过反射或注解提取实体的主键值
        return KeyExtractor.extract(entity);
    }

    /**
     * 创建同步日志
     */
    private DataSyncLog createSyncLog(String entityType, String operation, 
                                     String recordKey, Object data) {
        return DataSyncLog.builder()
            .tableName(entityType)
            .operationType(operation)
            .recordKey(recordKey)
            .sourceData(JsonUtils.toJson(data))
            .syncStatus("03") // 处理中
            .retryCount(0)
            .createdTime(LocalDateTime.now())
            .build();
    }
}
