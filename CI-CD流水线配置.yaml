# Jenkins Pipeline配置文件
# 返利系统微服务CI/CD流水线

apiVersion: v1
kind: ConfigMap
metadata:
  name: jenkins-pipeline-config
  namespace: devops
data:
  Jenkinsfile: |
    pipeline {
        agent any
        
        environment {
            DOCKER_REGISTRY = 'harbor.company.com'
            K8S_NAMESPACE = 'rebate-system'
            HELM_CHART_PATH = './helm-charts'
            SONAR_PROJECT_KEY = 'rebate-system'
        }
        
        parameters {
            choice(
                name: 'DEPLOY_ENV',
                choices: ['dev', 'test', 'staging', 'prod'],
                description: '选择部署环境'
            )
            choice(
                name: 'SERVICE_NAME',
                choices: ['all', 'contract-service', 'clause-service', 'agreement-service', 'calculation-service'],
                description: '选择要部署的服务'
            )
            booleanParam(
                name: 'SKIP_TESTS',
                defaultValue: false,
                description: '是否跳过测试'
            )
        }
        
        stages {
            stage('代码检出') {
                steps {
                    checkout scm
                    script {
                        env.GIT_COMMIT_SHORT = sh(
                            script: 'git rev-parse --short HEAD',
                            returnStdout: true
                        ).trim()
                        env.BUILD_VERSION = "${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"
                    }
                }
            }
            
            stage('代码质量检查') {
                parallel {
                    stage('SonarQube扫描') {
                        steps {
                            script {
                                def scannerHome = tool 'SonarQubeScanner'
                                withSonarQubeEnv('SonarQube') {
                                    sh """
                                        ${scannerHome}/bin/sonar-scanner \
                                        -Dsonar.projectKey=${SONAR_PROJECT_KEY} \
                                        -Dsonar.sources=src \
                                        -Dsonar.java.binaries=target/classes \
                                        -Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml
                                    """
                                }
                            }
                        }
                    }
                    
                    stage('安全扫描') {
                        steps {
                            sh '''
                                # OWASP依赖检查
                                mvn org.owasp:dependency-check-maven:check
                                
                                # Trivy容器镜像扫描
                                trivy fs --exit-code 1 --severity HIGH,CRITICAL .
                            '''
                        }
                    }
                }
            }
            
            stage('单元测试') {
                when {
                    not { params.SKIP_TESTS }
                }
                steps {
                    sh '''
                        mvn clean test
                        mvn jacoco:report
                    '''
                }
                post {
                    always {
                        publishTestResults testResultsPattern: 'target/surefire-reports/*.xml'
                        publishCoverage adapters: [
                            jacocoAdapter('target/site/jacoco/jacoco.xml')
                        ]
                    }
                }
            }
            
            stage('构建应用') {
                steps {
                    script {
                        if (params.SERVICE_NAME == 'all') {
                            // 构建所有服务
                            def services = [
                                'contract-service',
                                'clause-service', 
                                'agreement-service',
                                'calculation-service',
                                'mdd-service',
                                'approval-service',
                                'sync-service'
                            ]
                            
                            services.each { service ->
                                buildService(service)
                            }
                        } else {
                            // 构建指定服务
                            buildService(params.SERVICE_NAME)
                        }
                    }
                }
            }
            
            stage('构建Docker镜像') {
                steps {
                    script {
                        if (params.SERVICE_NAME == 'all') {
                            def services = [
                                'contract-service',
                                'clause-service',
                                'agreement-service', 
                                'calculation-service',
                                'mdd-service',
                                'approval-service',
                                'sync-service'
                            ]
                            
                            services.each { service ->
                                buildDockerImage(service)
                            }
                        } else {
                            buildDockerImage(params.SERVICE_NAME)
                        }
                    }
                }
            }
            
            stage('推送镜像') {
                steps {
                    script {
                        withCredentials([usernamePassword(
                            credentialsId: 'harbor-credentials',
                            usernameVariable: 'HARBOR_USER',
                            passwordVariable: 'HARBOR_PASS'
                        )]) {
                            sh """
                                echo ${HARBOR_PASS} | docker login ${DOCKER_REGISTRY} -u ${HARBOR_USER} --password-stdin
                            """
                            
                            if (params.SERVICE_NAME == 'all') {
                                def services = [
                                    'contract-service',
                                    'clause-service',
                                    'agreement-service',
                                    'calculation-service',
                                    'mdd-service', 
                                    'approval-service',
                                    'sync-service'
                                ]
                                
                                services.each { service ->
                                    pushDockerImage(service)
                                }
                            } else {
                                pushDockerImage(params.SERVICE_NAME)
                            }
                        }
                    }
                }
            }
            
            stage('部署到Kubernetes') {
                steps {
                    script {
                        withKubeConfig([credentialsId: 'k8s-config']) {
                            if (params.SERVICE_NAME == 'all') {
                                // 部署所有服务
                                sh """
                                    helm upgrade --install rebate-system ${HELM_CHART_PATH}/rebate-system \
                                    --namespace ${K8S_NAMESPACE} \
                                    --set global.image.tag=${BUILD_VERSION} \
                                    --set global.environment=${DEPLOY_ENV} \
                                    --wait --timeout=600s
                                """
                            } else {
                                // 部署指定服务
                                sh """
                                    helm upgrade --install ${params.SERVICE_NAME} ${HELM_CHART_PATH}/${params.SERVICE_NAME} \
                                    --namespace ${K8S_NAMESPACE} \
                                    --set image.tag=${BUILD_VERSION} \
                                    --set environment=${DEPLOY_ENV} \
                                    --wait --timeout=300s
                                """
                            }
                        }
                    }
                }
            }
            
            stage('健康检查') {
                steps {
                    script {
                        // 等待服务启动
                        sleep(time: 30, unit: 'SECONDS')
                        
                        // 检查服务健康状态
                        sh """
                            kubectl get pods -n ${K8S_NAMESPACE} -l app.kubernetes.io/instance=rebate-system
                            kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=rebate-system -n ${K8S_NAMESPACE} --timeout=300s
                        """
                        
                        // API健康检查
                        sh """
                            # 检查API网关健康状态
                            curl -f http://api-gateway.${K8S_NAMESPACE}.svc.cluster.local:8080/actuator/health
                            
                            # 检查各微服务健康状态
                            curl -f http://contract-service.${K8S_NAMESPACE}.svc.cluster.local:8080/actuator/health
                            curl -f http://clause-service.${K8S_NAMESPACE}.svc.cluster.local:8080/actuator/health
                        """
                    }
                }
            }
            
            stage('集成测试') {
                when {
                    anyOf {
                        environment name: 'DEPLOY_ENV', value: 'test'
                        environment name: 'DEPLOY_ENV', value: 'staging'
                    }
                }
                steps {
                    sh '''
                        # 运行API集成测试
                        mvn test -Dtest=**/*IntegrationTest
                        
                        # 运行端到端测试
                        npm run test:e2e
                    '''
                }
                post {
                    always {
                        publishTestResults testResultsPattern: 'target/failsafe-reports/*.xml'
                    }
                }
            }
            
            stage('性能测试') {
                when {
                    environment name: 'DEPLOY_ENV', value: 'staging'
                }
                steps {
                    sh '''
                        # JMeter性能测试
                        jmeter -n -t performance-tests/rebate-system-load-test.jmx \
                               -l performance-results.jtl \
                               -e -o performance-report
                    '''
                }
                post {
                    always {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'performance-report',
                            reportFiles: 'index.html',
                            reportName: 'Performance Test Report'
                        ])
                    }
                }
            }
        }
        
        post {
            always {
                // 清理工作空间
                cleanWs()
            }
            
            success {
                script {
                    if (params.DEPLOY_ENV == 'prod') {
                        // 生产环境部署成功通知
                        slackSend(
                            channel: '#ops-alerts',
                            color: 'good',
                            message: """
                                ✅ 返利系统生产环境部署成功
                                服务: ${params.SERVICE_NAME}
                                版本: ${BUILD_VERSION}
                                构建: ${BUILD_URL}
                            """
                        )
                    }
                }
            }
            
            failure {
                // 部署失败通知
                slackSend(
                    channel: '#ops-alerts',
                    color: 'danger',
                    message: """
                        ❌ 返利系统部署失败
                        环境: ${params.DEPLOY_ENV}
                        服务: ${params.SERVICE_NAME}
                        构建: ${BUILD_URL}
                    """
                )
                
                // 发送邮件通知
                emailext(
                    subject: "返利系统部署失败 - ${params.DEPLOY_ENV}",
                    body: """
                        部署失败详情:
                        环境: ${params.DEPLOY_ENV}
                        服务: ${params.SERVICE_NAME}
                        版本: ${BUILD_VERSION}
                        构建日志: ${BUILD_URL}console
                    """,
                    to: "${env.CHANGE_AUTHOR_EMAIL}, <EMAIL>"
                )
            }
        }
    }
    
    // 构建服务函数
    def buildService(serviceName) {
        sh """
            cd ${serviceName}
            mvn clean package -DskipTests
        """
    }
    
    // 构建Docker镜像函数
    def buildDockerImage(serviceName) {
        sh """
            cd ${serviceName}
            docker build -t ${DOCKER_REGISTRY}/rebate-system/${serviceName}:${BUILD_VERSION} .
            docker tag ${DOCKER_REGISTRY}/rebate-system/${serviceName}:${BUILD_VERSION} \
                       ${DOCKER_REGISTRY}/rebate-system/${serviceName}:latest
        """
    }
    
    // 推送Docker镜像函数
    def pushDockerImage(serviceName) {
        sh """
            docker push ${DOCKER_REGISTRY}/rebate-system/${serviceName}:${BUILD_VERSION}
            docker push ${DOCKER_REGISTRY}/rebate-system/${serviceName}:latest
        """
    }
