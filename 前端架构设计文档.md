# 返利系统前端架构设计

## 1. 技术选型

### 1.1 核心技术栈
- **Amis低代码框架**: 快速构建管理后台界面
- **React 18**: 现代化前端框架
- **TypeScript**: 类型安全的JavaScript
- **Ant Design**: 企业级UI组件库
- **Axios**: HTTP客户端
- **React Query**: 数据获取和缓存
- **Zustand**: 轻量级状态管理

### 1.2 架构优势
- **快速开发**: Amis低代码减少80%的前端开发工作量
- **类型安全**: TypeScript提供完整的类型检查
- **组件复用**: 基于Amis的组件化开发
- **响应式设计**: 适配各种屏幕尺寸
- **国际化支持**: 多语言切换能力

## 2. 项目结构

```
src/
├── components/           # 公共组件
│   ├── AmisRenderer/    # Amis渲染器封装
│   ├── Layout/          # 布局组件
│   ├── Charts/          # 图表组件
│   └── Business/        # 业务组件
├── pages/               # 页面组件
│   ├── Contract/        # 合同管理
│   ├── Clause/          # 条款管理
│   ├── Agreement/       # 协议管理
│   └── Dashboard/       # 仪表板
├── schemas/             # Amis页面配置
│   ├── contract/        # 合同相关页面
│   ├── clause/          # 条款相关页面
│   └── common/          # 公共配置
├── services/            # API服务
├── stores/              # 状态管理
├── utils/               # 工具函数
├── types/               # TypeScript类型定义
└── constants/           # 常量定义
```

## 3. 核心组件设计

### 3.1 AmisRenderer组件

```typescript
interface AmisRendererProps {
  schema: any;
  data?: any;
  onEvent?: (event: string, data: any) => void;
  env?: any;
}

const AmisRenderer: React.FC<AmisRendererProps> = ({
  schema,
  data,
  onEvent,
  env
}) => {
  const defaultEnv = {
    fetcher: async (api: any) => {
      // 统一的API请求处理
      return await apiClient.request(api);
    },
    notify: (type: string, msg: string) => {
      // 统一的消息提示
      notification[type]({ message: msg });
    },
    alert: (msg: string) => {
      Modal.info({ content: msg });
    },
    confirm: (msg: string) => {
      return new Promise((resolve) => {
        Modal.confirm({
          content: msg,
          onOk: () => resolve(true),
          onCancel: () => resolve(false)
        });
      });
    }
  };

  return (
    <div className="amis-renderer">
      {render(schema, {
        ...data,
        ...defaultEnv,
        ...env
      }, onEvent)}
    </div>
  );
};
```

### 3.2 业务组件封装

```typescript
// 合同状态组件
const ContractStatus: React.FC<{status: string}> = ({ status }) => {
  const statusConfig = {
    '01': { text: '草稿', color: 'default' },
    '02': { text: '待审批', color: 'processing' },
    '03': { text: '已审批', color: 'success' },
    '04': { text: '已拒绝', color: 'error' }
  };

  const config = statusConfig[status] || statusConfig['01'];
  
  return <Tag color={config.color}>{config.text}</Tag>;
};

// 审批流程组件
const ApprovalFlow: React.FC<{contractId: string}> = ({ contractId }) => {
  const { data: approvalHistory } = useQuery(
    ['approval-history', contractId],
    () => api.getApprovalHistory(contractId)
  );

  return (
    <Timeline>
      {approvalHistory?.map((item, index) => (
        <Timeline.Item
          key={index}
          color={item.status === 'approved' ? 'green' : 'red'}
        >
          <div>
            <div>{item.approverName} - {item.action}</div>
            <div className="text-gray-500">{item.timestamp}</div>
            {item.comment && <div>{item.comment}</div>}
          </div>
        </Timeline.Item>
      ))}
    </Timeline>
  );
};
```

## 4. 状态管理

### 4.1 全局状态设计

```typescript
interface AppState {
  user: UserInfo | null;
  permissions: string[];
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  loading: boolean;
}

const useAppStore = create<AppState>((set, get) => ({
  user: null,
  permissions: [],
  theme: 'light',
  language: 'zh-CN',
  loading: false,

  setUser: (user: UserInfo) => set({ user }),
  setPermissions: (permissions: string[]) => set({ permissions }),
  setTheme: (theme: 'light' | 'dark') => set({ theme }),
  setLanguage: (language: 'zh-CN' | 'en-US') => set({ language }),
  setLoading: (loading: boolean) => set({ loading })
}));
```

### 4.2 业务状态管理

```typescript
interface ContractState {
  currentContract: ContractInfo | null;
  contractList: ContractInfo[];
  searchParams: ContractSearchParams;
  pagination: PaginationInfo;
}

const useContractStore = create<ContractState>((set, get) => ({
  currentContract: null,
  contractList: [],
  searchParams: {},
  pagination: { current: 1, pageSize: 20, total: 0 },

  setCurrentContract: (contract: ContractInfo) => 
    set({ currentContract: contract }),
  
  setContractList: (list: ContractInfo[]) => 
    set({ contractList: list }),
  
  updateSearchParams: (params: Partial<ContractSearchParams>) =>
    set(state => ({ 
      searchParams: { ...state.searchParams, ...params }
    })),
  
  updatePagination: (pagination: Partial<PaginationInfo>) =>
    set(state => ({ 
      pagination: { ...state.pagination, ...pagination }
    }))
}));
```

## 5. API服务层

### 5.1 API客户端封装

```typescript
class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: process.env.REACT_APP_API_BASE_URL,
      timeout: 30000
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        if (response.data.code === 200) {
          return response.data.data;
        } else {
          throw new Error(response.data.message);
        }
      },
      (error) => {
        if (error.response?.status === 401) {
          // 处理未授权
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async request(config: AxiosRequestConfig) {
    return await this.instance.request(config);
  }
}

export const apiClient = new ApiClient();
```

### 5.2 业务API服务

```typescript
export const contractApi = {
  // 获取合同列表
  getContracts: (params: ContractQueryParams): Promise<PageResult<ContractInfo>> =>
    apiClient.request({
      method: 'GET',
      url: '/api/v1/contracts',
      params
    }),

  // 获取合同详情
  getContract: (id: string): Promise<ContractInfo> =>
    apiClient.request({
      method: 'GET',
      url: `/api/v1/contracts/${id}`
    }),

  // 创建合同
  createContract: (data: ContractCreateRequest): Promise<ContractInfo> =>
    apiClient.request({
      method: 'POST',
      url: '/api/v1/contracts',
      data
    }),

  // 更新合同
  updateContract: (id: string, data: ContractUpdateRequest): Promise<ContractInfo> =>
    apiClient.request({
      method: 'PUT',
      url: `/api/v1/contracts/${id}`,
      data
    }),

  // 提交审批
  submitApproval: (id: string): Promise<void> =>
    apiClient.request({
      method: 'POST',
      url: `/api/v1/contracts/${id}/submit`
    })
};
```

## 6. 页面配置管理

### 6.1 动态页面配置

```typescript
// 页面配置管理器
class PageConfigManager {
  private configs: Map<string, any> = new Map();

  // 注册页面配置
  register(pageId: string, config: any) {
    this.configs.set(pageId, config);
  }

  // 获取页面配置
  getConfig(pageId: string, params?: any): any {
    const config = this.configs.get(pageId);
    if (!config) {
      throw new Error(`Page config not found: ${pageId}`);
    }

    // 支持动态参数替换
    return this.processConfig(config, params);
  }

  // 处理配置中的动态参数
  private processConfig(config: any, params?: any): any {
    if (!params) return config;

    const configStr = JSON.stringify(config);
    const processedStr = configStr.replace(
      /\$\{(\w+)\}/g,
      (match, key) => params[key] || match
    );

    return JSON.parse(processedStr);
  }
}

export const pageConfigManager = new PageConfigManager();
```

### 6.2 权限控制

```typescript
// 权限检查Hook
const usePermission = () => {
  const { permissions } = useAppStore();

  const hasPermission = (permission: string): boolean => {
    return permissions.includes(permission);
  };

  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => permissions.includes(permission));
  };

  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => permissions.includes(permission));
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions
  };
};

// 权限组件
const PermissionGuard: React.FC<{
  permission: string | string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ permission, children, fallback = null }) => {
  const { hasPermission, hasAnyPermission } = usePermission();

  const hasAccess = Array.isArray(permission)
    ? hasAnyPermission(permission)
    : hasPermission(permission);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};
```

## 7. 性能优化

### 7.1 代码分割

```typescript
// 路由懒加载
const ContractManagement = lazy(() => import('../pages/Contract'));
const ClauseManagement = lazy(() => import('../pages/Clause'));
const AgreementManagement = lazy(() => import('../pages/Agreement'));

// 组件懒加载
const LazyComponent = lazy(() => import('./HeavyComponent'));

const App = () => (
  <Suspense fallback={<Loading />}>
    <Router>
      <Routes>
        <Route path="/contracts" element={<ContractManagement />} />
        <Route path="/clauses" element={<ClauseManagement />} />
        <Route path="/agreements" element={<AgreementManagement />} />
      </Routes>
    </Router>
  </Suspense>
);
```

### 7.2 缓存策略

```typescript
// React Query配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      refetchOnWindowFocus: false
    }
  }
});

// 预加载关键数据
const usePreloadData = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    // 预加载字典数据
    queryClient.prefetchQuery(['dict', 'contract-types'], 
      () => api.getDictionary('contract-types'));
    
    queryClient.prefetchQuery(['dict', 'company-codes'], 
      () => api.getDictionary('company-codes'));
  }, [queryClient]);
};
```

## 8. 国际化支持

```typescript
// 国际化配置
const i18nConfig = {
  'zh-CN': {
    'contract.title': '合同管理',
    'contract.create': '创建合同',
    'contract.edit': '编辑合同',
    'contract.status.draft': '草稿',
    'contract.status.pending': '待审批'
  },
  'en-US': {
    'contract.title': 'Contract Management',
    'contract.create': 'Create Contract',
    'contract.edit': 'Edit Contract',
    'contract.status.draft': 'Draft',
    'contract.status.pending': 'Pending'
  }
};

// 国际化Hook
const useI18n = () => {
  const { language } = useAppStore();
  
  const t = (key: string, params?: Record<string, any>): string => {
    let text = i18nConfig[language]?.[key] || key;
    
    if (params) {
      Object.keys(params).forEach(param => {
        text = text.replace(`{${param}}`, params[param]);
      });
    }
    
    return text;
  };

  return { t };
};
```
