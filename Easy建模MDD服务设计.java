/**
 * Easy建模MDD服务 - 模型驱动开发接口
 * 提供动态模型定义、代码生成、接口生成等功能
 */
@RestController
@RequestMapping("/api/v1/mdd")
@Slf4j
public class MDDController {

    @Autowired
    private MDDService mddService;
    
    @Autowired
    private ModelDefinitionService modelDefinitionService;
    
    @Autowired
    private CodeGenerationService codeGenerationService;

    /**
     * 创建数据模型
     */
    @PostMapping("/models")
    @Operation(summary = "创建数据模型", description = "基于JSON Schema创建数据模型")
    public ResponseEntity<ApiResponse<ModelDefinitionDTO>> createModel(
            @Valid @RequestBody ModelCreateRequest request) {
        
        try {
            ModelDefinitionDTO model = modelDefinitionService.createModel(request);
            return ResponseEntity.ok(ApiResponse.success(model));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("模型创建失败: " + e.getMessage()));
        }
    }

    /**
     * 生成CRUD接口
     */
    @PostMapping("/models/{modelId}/generate-api")
    @Operation(summary = "生成CRUD接口")
    public ResponseEntity<ApiResponse<GeneratedApiDTO>> generateCrudApi(
            @PathVariable String modelId,
            @RequestBody ApiGenerationRequest request) {
        
        try {
            GeneratedApiDTO result = codeGenerationService.generateCrudApi(modelId, request);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("接口生成失败: " + e.getMessage()));
        }
    }

    /**
     * 动态查询接口
     */
    @PostMapping("/models/{modelId}/query")
    @Operation(summary = "动态查询数据")
    public ResponseEntity<ApiResponse<Object>> dynamicQuery(
            @PathVariable String modelId,
            @RequestBody DynamicQueryRequest request) {
        
        try {
            Object result = mddService.dynamicQuery(modelId, request);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 动态保存接口
     */
    @PostMapping("/models/{modelId}/save")
    @Operation(summary = "动态保存数据")
    public ResponseEntity<ApiResponse<Object>> dynamicSave(
            @PathVariable String modelId,
            @RequestBody Map<String, Object> data) {
        
        try {
            Object result = mddService.dynamicSave(modelId, data);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("保存失败: " + e.getMessage()));
        }
    }
}

/**
 * MDD服务实现
 */
@Service
@Slf4j
public class MDDServiceImpl implements MDDService {

    @Autowired
    private ModelDefinitionRepository modelRepository;
    
    @Autowired
    private DynamicDataRepository dynamicDataRepository;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Object dynamicQuery(String modelId, DynamicQueryRequest request) {
        // 1. 获取模型定义
        ModelDefinition model = getModelDefinition(modelId);
        
        // 2. 构建动态SQL
        String sql = buildDynamicQuery(model, request);
        
        // 3. 执行查询
        try {
            if (request.isPagination()) {
                return executePageQuery(sql, request);
            } else {
                return executeListQuery(sql, request);
            }
        } catch (Exception e) {
            log.error("动态查询执行失败: modelId={}, sql={}", modelId, sql, e);
            throw new BusinessException("查询执行失败: " + e.getMessage());
        }
    }

    @Override
    public Object dynamicSave(String modelId, Map<String, Object> data) {
        // 1. 获取模型定义
        ModelDefinition model = getModelDefinition(modelId);
        
        // 2. 数据验证
        validateData(model, data);
        
        // 3. 构建保存SQL
        String sql = buildSaveSQL(model, data);
        
        // 4. 执行保存
        try {
            if (data.containsKey(model.getPrimaryKey()) && 
                data.get(model.getPrimaryKey()) != null) {
                // 更新操作
                return executeUpdate(sql, data, model);
            } else {
                // 插入操作
                return executeInsert(sql, data, model);
            }
        } catch (Exception e) {
            log.error("动态保存执行失败: modelId={}, data={}", modelId, data, e);
            throw new BusinessException("保存执行失败: " + e.getMessage());
        }
    }

    /**
     * 构建动态查询SQL
     */
    private String buildDynamicQuery(ModelDefinition model, DynamicQueryRequest request) {
        StringBuilder sql = new StringBuilder();
        
        // SELECT子句
        sql.append("SELECT ");
        if (request.getFields() != null && !request.getFields().isEmpty()) {
            sql.append(String.join(", ", request.getFields()));
        } else {
            sql.append("*");
        }
        
        // FROM子句
        sql.append(" FROM ").append(model.getTableName());
        
        // WHERE子句
        if (request.getConditions() != null && !request.getConditions().isEmpty()) {
            sql.append(" WHERE ");
            List<String> conditions = new ArrayList<>();
            
            for (QueryCondition condition : request.getConditions()) {
                String conditionSql = buildCondition(condition);
                conditions.add(conditionSql);
            }
            
            sql.append(String.join(" AND ", conditions));
        }
        
        // ORDER BY子句
        if (request.getOrderBy() != null && !request.getOrderBy().isEmpty()) {
            sql.append(" ORDER BY ");
            List<String> orderItems = new ArrayList<>();
            
            for (OrderByItem item : request.getOrderBy()) {
                orderItems.add(item.getField() + " " + item.getDirection());
            }
            
            sql.append(String.join(", ", orderItems));
        }
        
        // LIMIT子句 (分页)
        if (request.isPagination()) {
            int offset = (request.getPage() - 1) * request.getSize();
            sql.append(" LIMIT ").append(offset).append(", ").append(request.getSize());
        }
        
        return sql.toString();
    }

    /**
     * 构建条件SQL
     */
    private String buildCondition(QueryCondition condition) {
        String field = condition.getField();
        String operator = condition.getOperator();
        Object value = condition.getValue();
        
        switch (operator.toUpperCase()) {
            case "EQ":
                return field + " = '" + value + "'";
            case "NE":
                return field + " != '" + value + "'";
            case "GT":
                return field + " > '" + value + "'";
            case "GE":
                return field + " >= '" + value + "'";
            case "LT":
                return field + " < '" + value + "'";
            case "LE":
                return field + " <= '" + value + "'";
            case "LIKE":
                return field + " LIKE '%" + value + "%'";
            case "IN":
                if (value instanceof List) {
                    List<?> values = (List<?>) value;
                    String inValues = values.stream()
                        .map(v -> "'" + v + "'")
                        .collect(Collectors.joining(", "));
                    return field + " IN (" + inValues + ")";
                }
                break;
            case "BETWEEN":
                if (value instanceof Map) {
                    Map<String, Object> range = (Map<String, Object>) value;
                    return field + " BETWEEN '" + range.get("start") + "' AND '" + range.get("end") + "'";
                }
                break;
            case "IS_NULL":
                return field + " IS NULL";
            case "IS_NOT_NULL":
                return field + " IS NOT NULL";
        }
        
        throw new IllegalArgumentException("不支持的操作符: " + operator);
    }

    /**
     * 执行分页查询
     */
    private PageResult<Map<String, Object>> executePageQuery(String sql, DynamicQueryRequest request) {
        // 执行数据查询
        List<Map<String, Object>> records = jdbcTemplate.queryForList(sql);
        
        // 执行总数查询
        String countSql = "SELECT COUNT(*) " + sql.substring(sql.indexOf("FROM"));
        // 移除ORDER BY和LIMIT子句
        countSql = countSql.replaceAll("ORDER BY[^)]*", "");
        countSql = countSql.replaceAll("LIMIT[^)]*", "");
        
        Long total = jdbcTemplate.queryForObject(countSql, Long.class);
        
        return PageResult.<Map<String, Object>>builder()
            .records(records)
            .total(total != null ? total : 0L)
            .current(request.getPage())
            .size(request.getSize())
            .build();
    }

    /**
     * 执行列表查询
     */
    private List<Map<String, Object>> executeListQuery(String sql, DynamicQueryRequest request) {
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 数据验证
     */
    private void validateData(ModelDefinition model, Map<String, Object> data) {
        for (FieldDefinition field : model.getFields()) {
            Object value = data.get(field.getName());
            
            // 必填验证
            if (field.isRequired() && (value == null || value.toString().trim().isEmpty())) {
                throw new ValidationException("字段 " + field.getName() + " 不能为空");
            }
            
            // 类型验证
            if (value != null && !isValidType(value, field.getType())) {
                throw new ValidationException("字段 " + field.getName() + " 类型不匹配");
            }
            
            // 长度验证
            if (value != null && field.getMaxLength() != null) {
                if (value.toString().length() > field.getMaxLength()) {
                    throw new ValidationException("字段 " + field.getName() + " 长度超出限制");
                }
            }
            
            // 自定义验证规则
            if (field.getValidationRules() != null) {
                validateCustomRules(field, value);
            }
        }
    }

    /**
     * 获取模型定义 (带缓存)
     */
    private ModelDefinition getModelDefinition(String modelId) {
        String cacheKey = "model:definition:" + modelId;
        
        // 先从缓存获取
        ModelDefinition cached = (ModelDefinition) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 从数据库获取
        ModelDefinition model = modelRepository.findById(modelId)
            .orElseThrow(() -> new BusinessException("模型不存在: " + modelId));
        
        // 缓存模型定义
        redisTemplate.opsForValue().set(cacheKey, model, Duration.ofHours(24));
        
        return model;
    }

    /**
     * 构建保存SQL
     */
    private String buildSaveSQL(ModelDefinition model, Map<String, Object> data) {
        if (data.containsKey(model.getPrimaryKey()) && 
            data.get(model.getPrimaryKey()) != null) {
            // 构建UPDATE SQL
            return buildUpdateSQL(model, data);
        } else {
            // 构建INSERT SQL
            return buildInsertSQL(model, data);
        }
    }

    private String buildInsertSQL(ModelDefinition model, Map<String, Object> data) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(model.getTableName()).append(" (");
        
        List<String> fields = new ArrayList<>();
        List<String> values = new ArrayList<>();
        
        for (String key : data.keySet()) {
            if (data.get(key) != null) {
                fields.add(key);
                values.add("?");
            }
        }
        
        sql.append(String.join(", ", fields));
        sql.append(") VALUES (");
        sql.append(String.join(", ", values));
        sql.append(")");
        
        return sql.toString();
    }

    private String buildUpdateSQL(ModelDefinition model, Map<String, Object> data) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(model.getTableName()).append(" SET ");
        
        List<String> setClause = new ArrayList<>();
        for (String key : data.keySet()) {
            if (!key.equals(model.getPrimaryKey())) {
                setClause.add(key + " = ?");
            }
        }
        
        sql.append(String.join(", ", setClause));
        sql.append(" WHERE ").append(model.getPrimaryKey()).append(" = ?");
        
        return sql.toString();
    }
}
