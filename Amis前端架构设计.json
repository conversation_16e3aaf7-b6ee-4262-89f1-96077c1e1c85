{"type": "page", "title": "返利系统管理平台", "aside": {"type": "nav", "stacked": true, "links": [{"label": "合同管理", "icon": "fa fa-file-contract", "children": [{"label": "合同列表", "url": "/contracts", "icon": "fa fa-list"}, {"label": "创建合同", "url": "/contracts/create", "icon": "fa fa-plus"}, {"label": "合同审批", "url": "/contracts/approval", "icon": "fa fa-check-circle"}]}, {"label": "条款管理", "icon": "fa fa-file-text", "children": [{"label": "条款列表", "url": "/clauses", "icon": "fa fa-list"}, {"label": "创建条款", "url": "/clauses/create", "icon": "fa fa-plus"}]}, {"label": "协议管理", "icon": "fa fa-handshake", "children": [{"label": "协议列表", "url": "/agreements", "icon": "fa fa-list"}, {"label": "协议处理", "url": "/agreements/process", "icon": "fa fa-cogs"}]}, {"label": "返利计算", "icon": "fa fa-calculator", "children": [{"label": "计算引擎", "url": "/calculation", "icon": "fa fa-play"}, {"label": "计算结果", "url": "/calculation/results", "icon": "fa fa-chart-bar"}]}, {"label": "系统管理", "icon": "fa fa-cog", "children": [{"label": "用户管理", "url": "/users", "icon": "fa fa-users"}, {"label": "权限管理", "url": "/permissions", "icon": "fa fa-shield-alt"}, {"label": "系统配置", "url": "/config", "icon": "fa fa-wrench"}]}]}, "body": {"type": "router", "routes": [{"url": "/contracts", "schema": {"type": "page", "title": "合同管理", "toolbar": [{"type": "button", "label": "新建合同", "level": "primary", "actionType": "link", "link": "/contracts/create"}, {"type": "button", "label": "批量导入", "level": "info", "actionType": "dialog", "dialog": {"title": "批量导入合同", "body": {"type": "form", "api": "post:/api/v1/contracts/batch-import", "body": [{"type": "input-file", "name": "file", "label": "选择文件", "accept": ".xlsx,.xls", "required": true}]}}}], "body": [{"type": "form", "title": "查询条件", "mode": "inline", "target": "crud", "body": [{"type": "input-text", "name": "contractId", "label": "合同编号", "placeholder": "请输入合同编号"}, {"type": "select", "name": "contractType", "label": "合同类型", "source": "/api/v1/dict/contract-types", "clearable": true}, {"type": "select", "name": "status", "label": "状态", "options": [{"label": "草稿", "value": "01"}, {"label": "待审批", "value": "02"}, {"label": "已审批", "value": "03"}, {"label": "已拒绝", "value": "04"}], "clearable": true}, {"type": "input-date-range", "name": "date<PERSON><PERSON><PERSON>", "label": "创建日期", "format": "YYYY-MM-DD"}, {"type": "submit", "label": "查询"}, {"type": "reset", "label": "重置"}]}, {"type": "crud", "name": "crud", "api": "/api/v1/contracts", "defaultParams": {"page": 1, "size": 20}, "columns": [{"name": "contractId", "label": "合同编号", "type": "text", "searchable": true}, {"name": "contractDesc", "label": "合同描述", "type": "text"}, {"name": "contractType", "label": "合同类型", "type": "mapping", "source": "/api/v1/dict/contract-types"}, {"name": "partnerCode", "label": "伙伴编码", "type": "text"}, {"name": "startDate", "label": "开始日期", "type": "date"}, {"name": "endDate", "label": "结束日期", "type": "date"}, {"name": "status", "label": "状态", "type": "status", "map": {"01": {"label": "草稿", "color": "inactive"}, "02": {"label": "待审批", "color": "warning"}, "03": {"label": "已审批", "color": "success"}, "04": {"label": "已拒绝", "color": "danger"}}}, {"name": "created<PERSON>y", "label": "创建人", "type": "text"}, {"name": "createdDate", "label": "创建日期", "type": "date"}, {"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "查看", "level": "link", "actionType": "dialog", "dialog": {"title": "合同详情", "size": "lg", "body": {"type": "service", "api": "get:/api/v1/contracts/${contractId}", "body": {"type": "form", "mode": "horizontal", "disabled": true, "body": [{"type": "input-text", "name": "contractId", "label": "合同编号"}, {"type": "input-text", "name": "contractDesc", "label": "合同描述"}, {"type": "select", "name": "contractType", "label": "合同类型", "source": "/api/v1/dict/contract-types"}, {"type": "input-text", "name": "partnerCode", "label": "伙伴编码"}, {"type": "input-date", "name": "startDate", "label": "开始日期"}, {"type": "input-date", "name": "endDate", "label": "结束日期"}]}}}}, {"type": "button", "label": "编辑", "level": "primary", "actionType": "link", "link": "/contracts/edit/${contractId}", "visibleOn": "${status == '01'}"}, {"type": "button", "label": "提交审批", "level": "warning", "actionType": "ajax", "api": "post:/api/v1/contracts/${contractId}/submit", "confirmText": "确认提交该合同进行审批？", "visibleOn": "${status == '01'}"}, {"type": "button", "label": "删除", "level": "danger", "actionType": "ajax", "api": "delete:/api/v1/contracts/${contractId}", "confirmText": "确认删除该合同？", "visibleOn": "${status == '01'}"}]}]}]}}, {"url": "/contracts/create", "schema": {"type": "page", "title": "创建合同", "body": {"type": "form", "api": "post:/api/v1/contracts", "redirect": "/contracts", "body": [{"type": "tabs", "tabs": [{"title": "基本信息", "body": [{"type": "grid", "columns": [{"type": "select", "name": "contractType", "label": "合同类型", "source": "/api/v1/dict/contract-types", "required": true, "md": 6}, {"type": "select", "name": "companyCode", "label": "合同主体", "source": "/api/v1/dict/company-codes", "required": true, "md": 6}]}, {"type": "input-text", "name": "contractDesc", "label": "合同描述", "required": true, "maxLength": 100}, {"type": "grid", "columns": [{"type": "select", "name": "partnerType", "label": "伙伴类型", "options": [{"label": "供应商", "value": "S"}, {"label": "经销商", "value": "M"}], "required": true, "md": 6}, {"type": "picker", "name": "partnerCode", "label": "伙伴编码", "source": "/api/v1/partners/search?type=${partnerType}", "pickerSchema": {"type": "crud", "api": "/api/v1/partners?type=${partnerType}", "columns": [{"name": "code", "label": "编码"}, {"name": "name", "label": "名称"}]}, "required": true, "md": 6}]}, {"type": "grid", "columns": [{"type": "input-date", "name": "startDate", "label": "开始日期", "required": true, "md": 6}, {"type": "input-date", "name": "endDate", "label": "结束日期", "required": true, "md": 6}]}, {"type": "grid", "columns": [{"type": "input-text", "name": "<PERSON><PERSON><PERSON>", "label": "联系人", "md": 6}, {"type": "input-text", "name": "contactInfo", "label": "联系方式", "md": 6}]}]}, {"title": "商品组配置", "body": [{"type": "input-sub-form", "name": "productGroups", "label": "商品组", "multiple": true, "btnLabel": "添加商品组", "form": {"title": "商品组信息", "body": [{"type": "input-text", "name": "groupDesc", "label": "商品组描述", "required": true}, {"type": "select", "name": "usageType", "label": "用途类型", "options": [{"label": "任务商品组", "value": "T"}, {"label": "返利商品组", "value": "R"}]}, {"type": "input-table", "name": "items", "label": "商品明细", "columns": [{"name": "materialCode", "label": "物料编码", "type": "picker", "source": "/api/v1/materials/search", "required": true}, {"name": "priceMultiplier", "label": "价格倍数", "type": "input-number", "value": 1, "min": 0, "precision": 3}, {"name": "quantityMultiplier", "label": "数量倍数", "type": "input-number", "value": 1, "min": 0, "precision": 3}]}]}}]}]}, {"type": "divider"}, {"type": "button-group", "buttons": [{"type": "submit", "label": "保存", "level": "primary"}, {"type": "button", "label": "保存并提交审批", "level": "warning", "actionType": "submit", "api": "post:/api/v1/contracts?submitApproval=true"}, {"type": "button", "label": "取消", "actionType": "link", "link": "/contracts"}]}]}}}]}}